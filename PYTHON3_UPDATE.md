# Python 3 Update - Signatus2 API Demo

## Súhrn zmien

Všetky skripty a dokumentácia boli aktualizované na používanie `python3` namiesto `python` pre lepšiu kompatibilitu a jasnosť.

## 🔄 Aktualizované súbory

### 1. **Makefile**
✅ **Všetky príkazy aktualizované na python3:**
- `python3 -m venv venv` namiesto `python -m venv venv`
- `pip3 install` namiesto `pip install`
- `python3 signatus2_demo.py` namiesto `python signatus2_demo.py`
- `python3 -m pytest` namiesto `python -m pytest`
- `python3 -m flake8` namiesto `flake8`
- `python3 -m black` namiesto `black`
- `python3 -m mypy` namiesto `mypy`

✅ **Nové príkazy:**
- `make examples` - Spustenie príkladov použitia
- Rozš<PERSON><PERSON><PERSON> `make docs` s viac informáciami

✅ **Aktualizované help:**
- <PERSON><PERSON><PERSON><PERSON> oz<PERSON>e použitia Python 3
- Zobrazenie konkrétnych príkazov

### 2. **README.md**
✅ **Inštrukcie aktualizované:**
- `python3 -m venv venv` v sekcii inštalácie
- `pip3 install -r requirements.txt`
- `python3 signatus2_demo.py` v príkladoch spustenia
- `python3 -m pytest` v testovaní

### 3. **CHANGELOG.md**
✅ **Príklady spustenia:**
- Všetky príkazy aktualizované na `python3`
- Konzistentné používanie `pip3`

### 4. **Python súbory**
✅ **Shebang riadky skontrolované:**
- `signatus2_demo.py` - `#!/usr/bin/env python3`
- `run_demo.py` - `#!/usr/bin/env python3`
- `examples.py` - `#!/usr/bin/env python3`
- `test_signatus2_demo.py` - `#!/usr/bin/env python3`

## 🚀 Nové príkazy Makefile

```bash
# Základné príkazy (aktualizované)
make venv          # python3 -m venv venv
make install       # pip3 install -r requirements.txt
make run           # python3 signatus2_demo.py
make demo          # python3 run_demo.py
make examples      # python3 examples.py (NOVÉ)
make test          # python3 -m pytest test_signatus2_demo.py -v

# Kvalita kódu (aktualizované)
make lint          # python3 -m flake8
make format        # python3 -m black
make check         # python3 -m mypy

# Informácie (aktualizované)
make info          # python3 --version, pip3 --version, atď.
make docs          # Rozšírená dokumentácia
```

## 📋 Rýchly test aktualizácie

```bash
# Test základných príkazov
make help          # Zobrazenie aktualizovaného help
make info          # Kontrola Python 3 verzie
make venv          # Vytvorenie venv s python3
source venv/bin/activate
make install       # Inštalácia s pip3
make run           # Spustenie demo s python3
make test          # Testy s python3
make examples      # Nové - spustenie príkladov
```

## 🔧 Kompatibilita

### Podporované verzie Python
- **Python 3.7+** (minimálna verzia)
- **Python 3.8+** (odporúčané)
- **Python 3.9+** (optimálne)
- **Python 3.10+** (najnovšie funkcie)

### Systémové požiadavky
- **Linux/macOS**: `python3` a `pip3` štandardne dostupné
- **Windows**: Inštalácia Python 3 z python.org
- **Ubuntu/Debian**: `sudo apt install python3 python3-pip python3-venv`
- **CentOS/RHEL**: `sudo yum install python3 python3-pip`

## ⚠️ Dôležité poznámky

### Pre vývojárov
1. **Vždy používajte `python3`** namiesto `python`
2. **Vždy používajte `pip3`** namiesto `pip`
3. **Virtuálne prostredie** vytvárajte s `python3 -m venv`
4. **Testy spúšťajte** s `python3 -m pytest`

### Pre používateľov
1. **Skontrolujte verziu Python**: `python3 --version`
2. **Ak nemáte Python 3**: Nainštalujte z python.org
3. **Používajte Makefile**: Všetky príkazy sú už správne nastavené
4. **Pri problémoch**: Spustite `make info` pre diagnostiku

## 🐛 Riešenie problémov

### Python 3 nie je nájdený
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install python3 python3-pip python3-venv

# CentOS/RHEL
sudo yum install python3 python3-pip

# macOS (s Homebrew)
brew install python3

# Windows
# Stiahnite z https://python.org/downloads/
```

### pip3 nie je nájdený
```bash
# Ubuntu/Debian
sudo apt install python3-pip

# CentOS/RHEL
sudo yum install python3-pip

# macOS
python3 -m ensurepip --upgrade

# Windows
python3 -m ensurepip --upgrade
```

### Virtuálne prostredie nefunguje
```bash
# Inštalácia venv modulu
sudo apt install python3-venv  # Ubuntu/Debian
sudo yum install python3-venv  # CentOS/RHEL

# Alternatívne použitie virtualenv
pip3 install virtualenv
virtualenv -p python3 venv
```

## ✅ Overenie aktualizácie

Spustite nasledujúce príkazy pre overenie správnej aktualizácie:

```bash
# 1. Kontrola help
make help

# 2. Kontrola verzie Python
make info

# 3. Test vytvorenia venv
make venv

# 4. Test inštalácie
source venv/bin/activate  # Linux/macOS
# alebo venv\Scripts\activate  # Windows
make install

# 5. Test spustenia
make run

# 6. Test nového príkazu
make examples

# 7. Test testov
make test
```

Všetky príkazy by mali fungovať s Python 3 bez chýb.

---

**Aktualizácia dokončená:** Január 2024  
**Python verzia:** 3.7+  
**Kompatibilita:** Zachovaná pre všetky funkcie
