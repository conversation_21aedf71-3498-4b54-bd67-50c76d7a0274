["test_signatus2_demo.py::TestConfig::test_default_config", "test_signatus2_demo.py::TestConfig::test_env_config", "test_signatus2_demo.py::TestExceptions::test_authentication_error", "test_signatus2_demo.py::TestExceptions::test_conflict_error", "test_signatus2_demo.py::TestExceptions::test_not_found_error", "test_signatus2_demo.py::TestExceptions::test_signatus2_error", "test_signatus2_demo.py::TestIntegration::test_real_authentication", "test_signatus2_demo.py::TestIntegration::test_real_envelope_operations", "test_signatus2_demo.py::TestSignatus2Client::test_authenticate_failure", "test_signatus2_demo.py::TestSignatus2Client::test_authenticate_success", "test_signatus2_demo.py::TestSignatus2Client::test_basic_auth_header", "test_signatus2_demo.py::TestSignatus2Client::test_client_initialization", "test_signatus2_demo.py::TestSignatus2Client::test_context_manager", "test_signatus2_demo.py::TestSignatus2Client::test_create_envelope_success", "test_signatus2_demo.py::TestSignatus2Client::test_delete_envelope_success", "test_signatus2_demo.py::TestSignatus2Client::test_ensure_authenticated_with_expired_token", "test_signatus2_demo.py::TestSignatus2Client::test_get_envelope_complete_success", "test_signatus2_demo.py::TestSignatus2Client::test_get_envelope_success", "test_signatus2_demo.py::TestSignatus2Client::test_get_process_info_success", "test_signatus2_demo.py::TestSignatus2Client::test_handle_response_401", "test_signatus2_demo.py::TestSignatus2Client::test_handle_response_404", "test_signatus2_demo.py::TestSignatus2Client::test_handle_response_409", "test_signatus2_demo.py::TestSignatus2Client::test_handle_response_success", "test_signatus2_demo.py::TestUtilityFunctions::test_load_sample_pdf"]