# Signatus2 API Demo - Python Dependencies
# 
# Inštalácia: pip install -r requirements.txt

# HTTP klient pre REST API komunikáciu
requests>=2.31.0

# JSON spracovanie (sú<PERSON><PERSON><PERSON> štandardnej kni<PERSON>, ale explicitne uvedené pre jas<PERSON>ť)
# json - built-in

# Base64 kódovanie (súčasť štandardnej knižnice)
# base64 - built-in

# Logging (súčasť štandardnej knižnice)
# logging - built-in

# Datetime manipulácia (súčasť štandardnej knižnice)
# datetime - built-in

# Type hints (súčasť štandardnej knižnice od Python 3.5+)
# typing - built-in

# Environment variables (súčasť štandardnej knižnice)
# os - built-in

# Voliteľné závislosti pre rozšírené funkcie:

# Pre lepšie formátovanie JSON výstupov
rich>=13.0.0

# Pre pokročilé HTTP funkcie
httpx>=0.25.0

# Pre validáciu dát
pydantic>=2.0.0

# Pre konfiguráciu cez súbory
python-dotenv>=1.0.0

# Pre testovanie
pytest>=7.0.0
pytest-mock>=3.10.0
responses>=0.23.0
