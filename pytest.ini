[tool:pytest]
markers =
    integration: marks tests as integration tests (deselect with '-m "not integration"')
    slow: marks tests as slow (deselect with '-m "not slow"')

testpaths = .
python_files = test_*.py
python_classes = Test*
python_functions = test_*

addopts = 
    -v
    --tb=short
    --strict-markers
    
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
