#!/usr/bin/env python3
"""
Signatus2 API - Príklady použitia

Tento súbor obsahuje praktické príklady použitia Signatus2Client
pre rôzne scenáre a use cases.
"""

import base64
import json
from datetime import datetime
from signatus2_demo import Signatus2Client, setup_logging, load_sample_pdf


def example_basic_workflow():
    """
    Príklad 1: Základný workflow (aktualizované API)

    Demonštruje vytvorenie obálky s dokumentom pomocou nového API.
    """
    print("=== Príklad 1: Základný workflow (nové API) ===")

    with Signatus2Client() as client:
        try:
            # Autentifikácia
            print("🔐 Autentifikácia...")
            client.authenticate()

            # Príprava dokumentu
            print("📄 Príprava dokumentu...")
            pdf_content = load_sample_pdf()
            documents_data = {
                "zmluva_2024.pdf": base64.b64decode(pdf_content)
            }
            print("✅ Dokument pripravený")

            # Vytvorenie obálky s dokumentom
            print("📁 Vytvorenie obálky s dokumentom...")
            envelope = client.create_envelope(
                name="Zmluva o poskytovaní služieb",
                envelope_type="default",
                state="ready",
                expiration_hours=72,
                result_url="https://firma.sk/result",
                lang="sk",
                email_from="<EMAIL>",
                name_from="Dodávateľ s.r.o.",
                email_to="<EMAIL>",
                documents_data=documents_data
            )
            envelope_id = envelope["id"]
            process_id = envelope.get("processes", [{}])[0].get("id") if envelope.get("processes") else None

            print(f"✅ Obálka vytvorená: {envelope_id}")
            if process_id:
                print(f"✅ Proces vytvorený: {process_id}")

            # Kontrola stavu obálky
            envelope_info = client.get_envelope(envelope_id)
            print(f"📋 Stav obálky: {envelope_info.get('state')}")

            # Kompletné informácie
            complete_info = client.get_envelope_complete(envelope_id)
            print(f"📋 Typ obálky: {complete_info.get('envelopetype')}")
            print(f"📋 Expirácia: {complete_info.get('expirationtime')}")

            # Informácie o procese
            if process_id:
                process_info = client.get_process_info(process_id)
                print(f"⚙️ Stav procesu: {process_info.get('state')}")
                print(f"⚙️ Podpisovateľ: {process_info.get('signer')}")
                print(f"⚙️ Typ podpisu: {process_info.get('sigtype')}")

        except Exception as e:
            print(f"❌ Chyba: {e}")


def example_multiple_recipients():
    """
    Príklad 2: Obálka s viacerými príjemcami
    
    Demonštruje pridanie viacerých príjemcov s rôznymi rolami.
    """
    print("\n=== Príklad 2: Viacerí príjemcovia ===")
    
    with Signatus2Client() as client:
        try:
            client.authenticate()
            
            # Vytvorenie obálky
            envelope = client.create_envelope(
                name="Trojstranná dohoda",
                description="Dohoda medzi troma stranami"
            )
            envelope_id = envelope["id"]
            
            # Pridanie viacerých príjemcov
            recipients = [
                {"email": "<EMAIL>", "name": "Prvá strana", "role": "signer"},
                {"email": "<EMAIL>", "name": "Druhá strana", "role": "signer"},
                {"email": "<EMAIL>", "name": "Svedok", "role": "viewer"}
            ]
            
            for recipient in recipients:
                client.add_recipient_to_envelope(
                    envelope_id=envelope_id,
                    email=recipient["email"],
                    name=recipient["name"],
                    role=recipient["role"]
                )
                print(f"✅ Pridaný {recipient['name']} ({recipient['role']})")
            
            # Kontrola príjemcov
            envelope_info = client.get_envelope(envelope_id)
            print(f"📊 Celkový počet príjemcov: {len(envelope_info.get('recipients', []))}")
            
        except Exception as e:
            print(f"❌ Chyba: {e}")


def example_multiple_documents():
    """
    Príklad 3: Obálka s viacerými dokumentmi
    
    Demonštruje pridanie viacerých dokumentov do jednej obálky.
    """
    print("\n=== Príklad 3: Viacero dokumentov ===")
    
    with Signatus2Client() as client:
        try:
            client.authenticate()
            
            # Vytvorenie obálky
            envelope = client.create_envelope(
                name="Kompletný balík dokumentov",
                description="Zmluva + prílohy"
            )
            envelope_id = envelope["id"]
            
            # Pridanie príjemcu
            client.add_recipient_to_envelope(
                envelope_id=envelope_id,
                email="<EMAIL>",
                name="Podpisovateľ",
                role="signer"
            )
            
            # Pridanie viacerých dokumentov
            documents = [
                {"name": "Hlavná_zmluva.pdf", "type": "application/pdf"},
                {"name": "Priloha_A.pdf", "type": "application/pdf"},
                {"name": "Cennik.pdf", "type": "application/pdf"}
            ]
            
            for doc in documents:
                pdf_content = load_sample_pdf()  # V reálnom použití by ste načítali rôzne súbory
                client.add_document_to_envelope(
                    envelope_id=envelope_id,
                    document_name=doc["name"],
                    document_content=pdf_content,
                    content_type=doc["type"]
                )
                print(f"✅ Pridaný dokument: {doc['name']}")
            
            # Kontrola dokumentov
            envelope_info = client.get_envelope(envelope_id)
            print(f"📊 Celkový počet dokumentov: {len(envelope_info.get('documents', []))}")
            
        except Exception as e:
            print(f"❌ Chyba: {e}")


def example_file_upload():
    """
    Príklad 4: Nahrávanie skutočného súboru
    
    Demonštruje načítanie a nahranie skutočného PDF súboru.
    """
    print("\n=== Príklad 4: Nahrávanie súboru ===")
    
    # Vytvorenie testovacieho PDF súboru
    test_file_path = "test_document.pdf"
    
    # Vytvorenie jednoduchého PDF obsahu
    pdf_content_bytes = b"""%PDF-1.4
1 0 obj<</Type/Catalog/Pages 2 0 R>>endobj
2 0 obj<</Type/Pages/Kids[3 0 R]/Count 1>>endobj
3 0 obj<</Type/Page/Parent 2 0 R/MediaBox[0 0 612 792]/Contents 4 0 R>>endobj
4 0 obj<</Length 44>>stream
BT /F1 12 Tf 72 720 Td (Test dokument) Tj ET
endstream endobj
xref 0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000206 00000 n 
trailer<</Size 5/Root 1 0 R>>
startxref 299
%%EOF"""
    
    try:
        # Vytvorenie testovacieho súboru
        with open(test_file_path, "wb") as f:
            f.write(pdf_content_bytes)
        print(f"📄 Vytvorený testovací súbor: {test_file_path}")
        
        with Signatus2Client() as client:
            client.authenticate()
            
            # Vytvorenie obálky
            envelope = client.create_envelope(
                name="Upload test",
                description="Test nahrávania súboru z disku"
            )
            envelope_id = envelope["id"]
            
            # Načítanie súboru z disku
            with open(test_file_path, "rb") as f:
                file_content = f.read()
            
            # Konverzia do Base64
            base64_content = base64.b64encode(file_content).decode()
            
            # Nahranie do obálky
            client.add_document_to_envelope(
                envelope_id=envelope_id,
                document_name="Nahraný_dokument.pdf",
                document_content=base64_content,
                content_type="application/pdf"
            )
            print("✅ Súbor úspešne nahraný do obálky")
            
    except Exception as e:
        print(f"❌ Chyba: {e}")
    finally:
        # Vyčistenie testovacieho súboru
        try:
            import os
            os.remove(test_file_path)
            print(f"🗑️ Testovací súbor vymazaný")
        except:
            pass


def example_error_handling():
    """
    Príklad 5: Správne spracovanie chýb
    
    Demonštruje ako správne spracovávať rôzne typy chýb.
    """
    print("\n=== Príklad 5: Spracovanie chýb ===")
    
    from signatus2_demo import AuthenticationError, NotFoundError, ConflictError, Signatus2Error
    
    with Signatus2Client() as client:
        try:
            # Test autentifikácie
            print("🔐 Test autentifikácie...")
            client.authenticate()
            print("✅ Autentifikácia úspešná")
            
        except AuthenticationError as e:
            print(f"❌ Chyba autentifikácie: {e}")
            print("💡 Skontrolujte prihlasovacie údaje")
            return
        except Exception as e:
            print(f"❌ Neočakávaná chyba pri autentifikácii: {e}")
            return
        
        # Test neexistujúcej obálky
        try:
            print("\n📋 Test neexistujúcej obálky...")
            client.get_envelope("neexistujuce_id_12345")
            
        except NotFoundError as e:
            print(f"✅ Správne zachytená 404 chyba: {e}")
        except Exception as e:
            print(f"❌ Neočakávaná chyba: {e}")
        
        # Test vytvorenia obálky s chybným názvom
        try:
            print("\n📁 Test vytvorenia obálky...")
            envelope = client.create_envelope(
                name="Test obálka - error handling",
                description="Test spracovanie chýb"
            )
            print(f"✅ Obálka vytvorená: {envelope['id']}")
            
        except ConflictError as e:
            print(f"✅ Správne zachytená 409 chyba: {e}")
        except Signatus2Error as e:
            print(f"✅ Správne zachytená API chyba: {e}")
            if e.response_data:
                print(f"📊 Detaily chyby: {json.dumps(e.response_data, indent=2)}")
        except Exception as e:
            print(f"❌ Neočakávaná chyba: {e}")


def example_envelope_status_monitoring():
    """
    Príklad 6: Monitorovanie stavu obálky
    
    Demonštruje ako sledovať stav obálky a jej dokumentov.
    """
    print("\n=== Príklad 6: Monitorovanie stavu ===")
    
    with Signatus2Client() as client:
        try:
            client.authenticate()
            
            # Vytvorenie obálky
            envelope = client.create_envelope(
                name="Monitorovaná obálka",
                description="Test monitorovania stavu"
            )
            envelope_id = envelope["id"]
            
            # Pridanie príjemcu a dokumentu
            client.add_recipient_to_envelope(
                envelope_id=envelope_id,
                email="<EMAIL>",
                name="Test Monitor",
                role="signer"
            )
            
            pdf_content = load_sample_pdf()
            client.add_document_to_envelope(
                envelope_id=envelope_id,
                document_name="Monitorovaný_dokument.pdf",
                document_content=pdf_content
            )
            
            # Monitorovanie stavu
            print("📊 Monitorovanie stavu obálky...")
            envelope_info = client.get_envelope(envelope_id)
            
            print(f"   Stav obálky: {envelope_info.get('state', 'neznámy')}")
            print(f"   Vytvorená: {envelope_info.get('created', 'N/A')}")
            print(f"   Posledná zmena: {envelope_info.get('modified', 'N/A')}")
            
            # Stav dokumentov
            documents = envelope_info.get('documents', [])
            print(f"   Dokumenty ({len(documents)}):")
            for doc in documents:
                print(f"     - {doc.get('name')}: {doc.get('status', 'neznámy')}")
            
            # Stav príjemcov
            recipients = envelope_info.get('recipients', [])
            print(f"   Príjemcovia ({len(recipients)}):")
            for recipient in recipients:
                status = recipient.get('status', 'neznámy')
                print(f"     - {recipient.get('name')}: {status}")
            
        except Exception as e:
            print(f"❌ Chyba: {e}")


def example_new_endpoints():
    """
    Príklad 7: Testovanie nových endpointov

    Demonštruje použitie všetkých nových API endpointov.
    """
    print("\n=== Príklad 7: Nové API endpointy ===")

    with Signatus2Client() as client:
        try:
            client.authenticate()

            # Vytvorenie obálky pre testovanie
            print("📁 Vytvorenie testovacej obálky...")
            pdf_content = load_sample_pdf()
            documents_data = {
                "test_endpoints.pdf": base64.b64decode(pdf_content)
            }

            envelope = client.create_envelope(
                name="Test nových endpointov",
                documents_data=documents_data
            )
            envelope_id = envelope["id"]
            process_id = envelope.get("processes", [{}])[0].get("id") if envelope.get("processes") else None

            print(f"✅ Testovacia obálka: {envelope_id}")

            # Test všetkých nových endpointov
            print("\n🧪 Testovanie endpointov...")

            # 1. Základné informácie o obálke
            print("1️⃣ Základné informácie o obálke...")
            basic_info = client.get_envelope(envelope_id)
            print(f"   Stav: {basic_info.get('state')}")

            # 2. Kompletné informácie o obálke
            print("2️⃣ Kompletné informácie o obálke...")
            complete_info = client.get_envelope_complete(envelope_id)
            print(f"   Typ: {complete_info.get('envelopetype')}")
            print(f"   Procesy: {len(complete_info.get('processes', []))}")

            # 3. Dáta obálky
            print("3️⃣ Dáta obálky...")
            envelope_data = client.get_envelope_data(envelope_id)
            print(f"   Dĺžka dát: {len(envelope_data)} znakov")

            # 4. Informácie o procese
            if process_id:
                print("4️⃣ Informácie o procese...")
                process_info = client.get_process_info(process_id)
                print(f"   Stav procesu: {process_info.get('state')}")
                print(f"   Podpisovateľ: {process_info.get('signer')}")

                # Dokumenty procesu
                docs_process = process_info.get('documentsProcess', [])
                print(f"   Dokumenty procesu: {len(docs_process)}")
                for doc in docs_process:
                    print(f"     - {doc.get('reference')} ({doc.get('method')})")

            # 5. Vymazanie obálky
            print("5️⃣ Vymazanie obálky...")
            delete_result = client.delete_envelope(envelope_id)
            print(f"   Výsledok: {delete_result.get('message', 'OK')}")

            print("✅ Všetky nové endpointy fungujú správne!")

        except Exception as e:
            print(f"❌ Chyba: {e}")


def example_configuration():
    """
    Príklad 8: Vlastná konfigurácia

    Demonštruje použitie vlastnej konfigurácie.
    """
    print("\n=== Príklad 8: Vlastná konfigurácia ===")

    from signatus2_demo import Config

    # Vytvorenie vlastnej konfigurácie
    custom_config = Config()
    custom_config.REQUEST_TIMEOUT = 60  # Dlhší timeout
    custom_config.MAX_RETRIES = 5       # Viac pokusov

    print(f"🔧 Vlastná konfigurácia:")
    print(f"   Timeout: {custom_config.REQUEST_TIMEOUT}s")
    print(f"   Max retries: {custom_config.MAX_RETRIES}")
    print(f"   API URL: {custom_config.API_BASE_URL}")

    # Použitie vlastnej konfigurácie
    with Signatus2Client(custom_config) as client:
        try:
            client.authenticate()
            print("✅ Klient s vlastnou konfiguráciou funguje")

        except Exception as e:
            print(f"❌ Chyba: {e}")


def main():
    """Spustenie všetkých príkladov"""
    setup_logging("INFO")

    print("🚀 Signatus2 API - Príklady použitia (Aktualizované)")
    print("=" * 60)

    examples = [
        example_basic_workflow,
        example_multiple_recipients,
        example_multiple_documents,
        example_file_upload,
        example_error_handling,
        example_envelope_status_monitoring,
        example_new_endpoints,
        example_configuration
    ]
    
    for i, example_func in enumerate(examples, 1):
        try:
            example_func()
        except KeyboardInterrupt:
            print(f"\n⏹️ Príklady prerušené používateľom na príklade {i}")
            break
        except Exception as e:
            print(f"\n❌ Chyba v príklade {i}: {e}")
        
        if i < len(examples):
            input("\n⏸️ Stlačte Enter pre pokračovanie na ďalší príklad...")
    
    print("\n🎉 Všetky príklady dokončené!")


if __name__ == "__main__":
    main()
