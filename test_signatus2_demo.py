#!/usr/bin/env python3
"""
Unit testy pre Signatus2 API Demo

Spustenie testov: python3 -m pytest test_signatus2_demo.py -v
"""

import pytest
import base64
from unittest.mock import Mock, patch
from datetime import datetime, timedelta

from signatus2_demo import (
    Signatus2Client, Config, Signatus2Error, AuthenticationError, 
    NotFoundError, ConflictError, load_sample_pdf
)


class TestConfig:
    """Testy pre Config triedu"""
    
    def test_default_config(self):
        """Test predvolených hodnôt konfigurácie"""
        config = Config()

        assert config.AUTH_URL == 'https://dsoauth.ana.sk/realms/Signatus/protocol/openid-connect/token'
        assert config.API_BASE_URL == 'https://ds-sts.ana.sk/signatus/api'  # Aktualizované
        assert config.CLIENT_ID == 'signatus'
        assert config.USERNAME == 'iauser'
        assert config.REQUEST_TIMEOUT == 30
        assert config.MAX_RETRIES == 3
        assert config.CSRF_TOKEN == '0bed913d-90bc-48be-ba95-55c8e63da864'  # Nové
    
    @patch.dict('os.environ', {
        'SIGNATUS_USERNAME': 'test_user',
        'SIGNATUS_PASSWORD': 'test_pass',
        'SIGNATUS_TIMEOUT': '60'
    }, clear=False)
    def test_env_config(self):
        """Test konfigurácie cez environment variables"""
        # Importujeme Config znovu aby sa načítali nové env variables
        import importlib
        import signatus2_demo
        importlib.reload(signatus2_demo)
        from signatus2_demo import Config

        config = Config()

        assert config.USERNAME == 'test_user'
        assert config.PASSWORD == 'test_pass'
        assert config.REQUEST_TIMEOUT == 60


class TestSignatus2Client:
    """Testy pre Signatus2Client triedu"""
    
    @pytest.fixture
    def client(self):
        """Fixture pre vytvorenie test klienta"""
        return Signatus2Client()
    
    @pytest.fixture
    def mock_response(self):
        """Fixture pre mock HTTP odpoveď"""
        response = Mock()
        response.status_code = 200
        response.content = b'{"test": "data"}'
        response.json.return_value = {"test": "data"}
        return response
    
    def test_client_initialization(self, client):
        """Test inicializácie klienta"""
        assert client.access_token is None
        assert client.token_expires_at is None
        assert client.config is not None
        assert client.session is not None
    
    def test_basic_auth_header(self, client):
        """Test generovania Basic Auth hlavičky"""
        header = client._get_basic_auth_header()
        
        expected_credentials = f"{client.config.CLIENT_ID}:{client.config.CLIENT_SECRET}"
        expected_encoded = base64.b64encode(expected_credentials.encode()).decode()
        expected_header = f"Basic {expected_encoded}"
        
        assert header == expected_header
    
    def test_handle_response_success(self, client, mock_response):
        """Test spracovania úspešnej odpovede"""
        result = client._handle_response(mock_response)
        assert result == {"test": "data"}
    
    def test_handle_response_401(self, client):
        """Test spracovania 401 chyby"""
        response = Mock()
        response.status_code = 401
        response.content = b'{"error": {"message": "Unauthorized"}}'
        response.json.return_value = {"error": {"message": "Unauthorized"}}

        with pytest.raises(AuthenticationError) as exc_info:
            client._handle_response(response)

        assert exc_info.value.status_code == 401
        assert "Neplatná autentifikácia" in str(exc_info.value)

    def test_handle_response_404(self, client):
        """Test spracovania 404 chyby"""
        response = Mock()
        response.status_code = 404
        response.content = b'{"error": {"message": "Not found"}}'
        response.json.return_value = {"error": {"message": "Not found"}}

        with pytest.raises(NotFoundError) as exc_info:
            client._handle_response(response)

        assert exc_info.value.status_code == 404
        assert "Zdroj nebol nájdený" in str(exc_info.value)

    def test_handle_response_409(self, client):
        """Test spracovania 409 chyby"""
        response = Mock()
        response.status_code = 409
        response.content = b'{"error": {"message": "Conflict"}}'
        response.json.return_value = {"error": {"message": "Conflict"}}

        with pytest.raises(ConflictError) as exc_info:
            client._handle_response(response)

        assert exc_info.value.status_code == 409
        assert "Konflikt pri spracovaní" in str(exc_info.value)
    
    @patch('signatus2_demo.requests.Session.post')
    def test_authenticate_success(self, mock_post, client):
        """Test úspešnej autentifikácie"""
        # Mock odpoveď
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "access_token": "test_token_123",
            "expires_in": 3600,
            "token_type": "Bearer"
        }
        mock_post.return_value = mock_response
        
        # Test autentifikácie
        token = client.authenticate()
        
        assert token == "test_token_123"
        assert client.access_token == "test_token_123"
        assert client.token_expires_at is not None
        
        # Overenie volania
        mock_post.assert_called_once()
        call_args = mock_post.call_args
        assert client.config.AUTH_URL in str(call_args)
    
    @patch('signatus2_demo.requests.Session.post')
    def test_authenticate_failure(self, mock_post, client):
        """Test neúspešnej autentifikácie"""
        # Mock chybovej odpovede
        mock_response = Mock()
        mock_response.status_code = 401
        mock_response.content = b'{"error": "invalid_grant"}'
        mock_response.json.return_value = {"error": "invalid_grant"}
        mock_post.return_value = mock_response

        # Test chyby autentifikácie
        with pytest.raises(AuthenticationError) as exc_info:
            client.authenticate()

        assert exc_info.value.status_code == 401
    
    @patch('signatus2_demo.requests.Session.get')
    def test_get_envelope_success(self, mock_get, client):
        """Test úspešného získania obálky"""
        # Nastavenie tokenu
        client.access_token = "test_token"
        client.token_expires_at = datetime.now() + timedelta(hours=1)
        
        # Mock odpoveď
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "id": "envelope_123",
            "state": "draft",
            "name": "Test envelope"
        }
        mock_get.return_value = mock_response
        
        # Test získania obálky
        result = client.get_envelope("envelope_123")
        
        assert result["id"] == "envelope_123"
        assert result["state"] == "draft"
        
        # Overenie volania
        mock_get.assert_called_once()
        call_args = mock_get.call_args
        assert "envelope_123" in str(call_args)
    
    @patch('signatus2_demo.requests.Session.post')
    def test_create_envelope_success(self, mock_post, client):
        """Test úspešného vytvorenia obálky (nové API)"""
        # Nastavenie tokenu
        client.access_token = "test_token"
        client.token_expires_at = datetime.now() + timedelta(hours=1)

        # Mock odpoveď
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "id": "envelope_456",
            "state": "ready",
            "expirationtime": 1234567890000,
            "processes": [{"id": "process_123"}]
        }
        mock_post.return_value = mock_response

        # Test vytvorenia obálky s dokumentom
        documents_data = {"test.pdf": b"fake_pdf_content"}
        result = client.create_envelope(
            name="New envelope",
            documents_data=documents_data
        )

        assert result["id"] == "envelope_456"
        assert result["state"] == "ready"
        assert len(result["processes"]) == 1

        # Overenie volania
        mock_post.assert_called_once()

        # Overenie že sa použil files parameter
        call_args = mock_post.call_args
        assert 'files' in call_args.kwargs

    @patch('signatus2_demo.requests.Session.get')
    def test_get_envelope_complete_success(self, mock_get, client):
        """Test úspešného získania kompletných informácií o obálke"""
        # Nastavenie tokenu
        client.access_token = "test_token"
        client.token_expires_at = datetime.now() + timedelta(hours=1)

        # Mock odpoveď
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "id": "envelope_123",
            "state": "ready",
            "envelopetype": "default",
            "expirationtime": 1234567890000,
            "processes": [
                {
                    "id": "process_456",
                    "state": "ready",
                    "signer": "user1",
                    "sigtype": "bio"
                }
            ]
        }
        mock_get.return_value = mock_response

        # Test získania kompletných informácií
        result = client.get_envelope_complete("envelope_123")

        assert result["id"] == "envelope_123"
        assert result["envelopetype"] == "default"
        assert len(result["processes"]) == 1
        assert result["processes"][0]["sigtype"] == "bio"

        # Overenie volania
        mock_get.assert_called_once()
        call_args = mock_get.call_args
        assert "envelope/complete/envelope_123" in str(call_args)

    @patch('signatus2_demo.requests.Session.get')
    def test_get_process_info_success(self, mock_get, client):
        """Test úspešného získania informácií o procese"""
        # Nastavenie tokenu
        client.access_token = "test_token"
        client.token_expires_at = datetime.now() + timedelta(hours=1)

        # Mock odpoveď
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "id": "process_123",
            "envelopeid": "envelope_456",
            "state": "ready",
            "signer": "user1",
            "sigtype": "bio",
            "signing": "document",
            "documentsProcess": [
                {
                    "documentid": "doc_789",
                    "reference": "test.pdf",
                    "method": "write"
                }
            ]
        }
        mock_get.return_value = mock_response

        # Test získania informácií o procese
        result = client.get_process_info("process_123")

        assert result["id"] == "process_123"
        assert result["envelopeid"] == "envelope_456"
        assert result["sigtype"] == "bio"
        assert len(result["documentsProcess"]) == 1

        # Overenie volania
        mock_get.assert_called_once()
        call_args = mock_get.call_args
        assert "process/process_123" in str(call_args)

    @patch('signatus2_demo.requests.Session.delete')
    def test_delete_envelope_success(self, mock_delete, client):
        """Test úspešného vymazania obálky"""
        # Nastavenie tokenu
        client.access_token = "test_token"
        client.token_expires_at = datetime.now() + timedelta(hours=1)

        # Mock odpoveď
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "message": "msg.envelope.delete.success"
        }
        mock_delete.return_value = mock_response

        # Test vymazania obálky
        result = client.delete_envelope("envelope_123")

        assert result["message"] == "msg.envelope.delete.success"

        # Overenie volania
        mock_delete.assert_called_once()
        call_args = mock_delete.call_args
        assert "envelope/envelope_123" in str(call_args)
    
    def test_context_manager(self):
        """Test context manager funkcionality"""
        with Signatus2Client() as client:
            assert client is not None
            assert client.session is not None
        
        # Session by mal byť zatvorený po ukončení context managera
        # (Nemôžeme priamo testovať zatvorenie, ale overíme že sa nezhodil)
    
    def test_ensure_authenticated_with_expired_token(self, client):
        """Test automatickej obnovy expirovaného tokenu"""
        # Nastavenie expirovaného tokenu
        client.access_token = "old_token"
        client.token_expires_at = datetime.now() - timedelta(minutes=1)
        
        with patch.object(client, 'authenticate') as mock_auth:
            mock_auth.return_value = "new_token"
            
            # Volanie metódy ktorá vyžaduje autentifikáciu
            client._ensure_authenticated()
            
            # Overenie že sa zavolala autentifikácia
            mock_auth.assert_called_once()


class TestUtilityFunctions:
    """Testy pre utility funkcie"""
    
    def test_load_sample_pdf(self):
        """Test načítania vzorového PDF"""
        pdf_content = load_sample_pdf()
        
        # Overenie že je to Base64 string
        assert isinstance(pdf_content, str)
        assert len(pdf_content) > 0
        
        # Overenie že sa dá dekódovať
        try:
            decoded = base64.b64decode(pdf_content)
            assert decoded.startswith(b'%PDF')
        except Exception:
            pytest.fail("PDF content nie je platný Base64")


class TestExceptions:
    """Testy pre custom výjimky"""
    
    def test_signatus2_error(self):
        """Test základnej Signatus2Error výjimky"""
        error = Signatus2Error("Test error", status_code=400, response_data={"error": "test"})
        
        assert str(error) == "Test error"
        assert error.status_code == 400
        assert error.response_data == {"error": "test"}
    
    def test_authentication_error(self):
        """Test AuthenticationError výjimky"""
        error = AuthenticationError("Auth failed", status_code=401)
        
        assert str(error) == "Auth failed"
        assert error.status_code == 401
        assert isinstance(error, Signatus2Error)
    
    def test_not_found_error(self):
        """Test NotFoundError výjimky"""
        error = NotFoundError("Resource not found", status_code=404)
        
        assert str(error) == "Resource not found"
        assert error.status_code == 404
        assert isinstance(error, Signatus2Error)
    
    def test_conflict_error(self):
        """Test ConflictError výjimky"""
        error = ConflictError("Conflict occurred", status_code=409)
        
        assert str(error) == "Conflict occurred"
        assert error.status_code == 409
        assert isinstance(error, Signatus2Error)


# Integračné testy (vyžadujú skutočné API pripojenie)
class TestIntegration:
    """Integračné testy - spúšťajte len s platným API prístupom"""
    
    @pytest.mark.integration
    @pytest.mark.skip(reason="Vyžaduje platné API credentials")
    def test_real_authentication(self):
        """Test skutočnej autentifikácie"""
        client = Signatus2Client()
        
        try:
            token = client.authenticate()
            assert token is not None
            assert len(token) > 0
        except AuthenticationError:
            pytest.skip("Neplatné API credentials")
    
    @pytest.mark.integration
    @pytest.mark.skip(reason="Vyžaduje platné API credentials")
    def test_real_envelope_operations(self):
        """Test skutočných operácií s obálkami"""
        client = Signatus2Client()
        
        try:
            # Autentifikácia
            client.authenticate()
            
            # Vytvorenie obálky
            envelope = client.create_envelope(
                name="Test envelope",
                description="Integration test envelope"
            )
            
            envelope_id = envelope["id"]
            assert envelope_id is not None
            
            # Získanie informácií o obálke
            envelope_info = client.get_envelope(envelope_id)
            assert envelope_info["id"] == envelope_id
            
        except (AuthenticationError, Signatus2Error) as e:
            pytest.skip(f"API chyba: {e}")


if __name__ == "__main__":
    # Spustenie testov
    pytest.main([__file__, "-v"])
