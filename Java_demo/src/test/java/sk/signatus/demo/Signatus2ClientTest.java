package sk.signatus.demo;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import okhttp3.mockwebserver.MockResponse;
import okhttp3.mockwebserver.MockWebServer;
import okhttp3.mockwebserver.RecordedRequest;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Disabled;
import sk.signatus.demo.exception.AuthenticationException;
import sk.signatus.demo.exception.NotFoundError;
import sk.signatus.demo.exception.Signatus2Exception;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit testy pre Signatus2Client
 */
@Disabled("Testy dočasne vypnuté - potrebujú refaktoring pre mock server")
class Signatus2ClientTest {
    
    private MockWebServer mockWebServer;
    private Signatus2Client client;
    private ObjectMapper objectMapper;
    
    @BeforeEach
    void setUp() throws IOException {
        mockWebServer = new MockWebServer();
        mockWebServer.start();

        // Vytvorenie klienta s custom HTTP klientom pre mock server
        okhttp3.OkHttpClient httpClient = new okhttp3.OkHttpClient.Builder()
                .connectTimeout(5, java.util.concurrent.TimeUnit.SECONDS)
                .readTimeout(5, java.util.concurrent.TimeUnit.SECONDS)
                .build();

        client = new Signatus2Client(httpClient) {
            @Override
            public String authenticate() throws sk.signatus.demo.exception.AuthenticationException, sk.signatus.demo.exception.Signatus2Exception {
                // Override pre test - použije mock server URL
                String authUrl = mockWebServer.url("/auth").toString();

                okhttp3.FormBody formBody = new okhttp3.FormBody.Builder()
                        .add("grant_type", "password")
                        .add("username", "test_user")
                        .add("password", "test_pass")
                        .add("scope", "openid")
                        .build();

                okhttp3.Request request = new okhttp3.Request.Builder()
                        .url(authUrl)
                        .header("Authorization", "Basic dGVzdDp0ZXN0")
                        .header("Content-Type", "application/x-www-form-urlencoded")
                        .post(formBody)
                        .build();

                try (okhttp3.Response response = httpClient.newCall(request).execute()) {
                    com.fasterxml.jackson.databind.JsonNode responseData = handleResponse(response);
                    sk.signatus.demo.model.TokenResponse tokenResponse = objectMapper.treeToValue(responseData, sk.signatus.demo.model.TokenResponse.class);
                    return tokenResponse.getAccessToken();
                } catch (Exception e) {
                    throw new sk.signatus.demo.exception.AuthenticationException("Test auth error: " + e.getMessage(), e);
                }
            }

            @Override
            public com.fasterxml.jackson.databind.JsonNode getEnvelope(String envelopeId) throws sk.signatus.demo.exception.Signatus2Exception {
                String url = mockWebServer.url("/api/envelope/" + envelopeId).toString();

                okhttp3.Request request = new okhttp3.Request.Builder()
                        .url(url)
                        .header("Authorization", "Bearer test_token")
                        .get()
                        .build();

                try (okhttp3.Response response = httpClient.newCall(request).execute()) {
                    return handleResponse(response);
                } catch (Exception e) {
                    throw new sk.signatus.demo.exception.Signatus2Exception("Test error: " + e.getMessage(), e);
                }
            }

            @Override
            public com.fasterxml.jackson.databind.JsonNode getProcessInfo(String processId) throws sk.signatus.demo.exception.Signatus2Exception {
                String url = mockWebServer.url("/api/process/" + processId).toString();

                okhttp3.Request request = new okhttp3.Request.Builder()
                        .url(url)
                        .header("Authorization", "Bearer test_token")
                        .get()
                        .build();

                try (okhttp3.Response response = httpClient.newCall(request).execute()) {
                    return handleResponse(response);
                } catch (Exception e) {
                    throw new sk.signatus.demo.exception.Signatus2Exception("Test error: " + e.getMessage(), e);
                }
            }
        };

        objectMapper = new ObjectMapper();
    }
    
    @AfterEach
    void tearDown() throws IOException {
        mockWebServer.shutdown();
        client.close();
        
        // Vyčistenie system properties
        System.clearProperty("SIGNATUS_AUTH_URL");
        System.clearProperty("SIGNATUS_API_URL");
    }
    
    @Test
    void testSuccessfulAuthentication() throws Exception {
        // Mock úspešnej autentifikácie
        String tokenResponse = "{\n" +
                "    \"access_token\": \"test_token_123\",\n" +
                "    \"token_type\": \"Bearer\",\n" +
                "    \"expires_in\": 3600,\n" +
                "    \"scope\": \"openid\"\n" +
                "}";
        
        mockWebServer.enqueue(new MockResponse()
                .setResponseCode(200)
                .setHeader("Content-Type", "application/json")
                .setBody(tokenResponse));
        
        // Test autentifikácie
        String token = client.authenticate();
        
        assertEquals("test_token_123", token);
        
        // Overenie požiadavky
        RecordedRequest request = mockWebServer.takeRequest();
        assertEquals("POST", request.getMethod());
        assertEquals("/auth", request.getPath());
        assertTrue(request.getHeader("Authorization").startsWith("Basic"));
        assertTrue(request.getBody().readUtf8().contains("grant_type=password"));
    }
    
    @Test
    void testAuthenticationFailure() {
        // Mock neúspešnej autentifikácie
        mockWebServer.enqueue(new MockResponse()
                .setResponseCode(401)
                .setHeader("Content-Type", "application/json")
                .setBody("{\"error\": \"invalid_grant\"}"));
        
        // Test chyby autentifikácie
        assertThrows(AuthenticationException.class, () -> client.authenticate());
    }
    
    @Test
    void testGetEnvelopeSuccess() throws Exception {
        // Mock autentifikácie
        mockWebServer.enqueue(new MockResponse()
                .setResponseCode(200)
                .setBody("{\"access_token\": \"test_token\", \"expires_in\": 3600}"));
        
        // Mock úspešného získania obálky
        String envelopeResponse = "{\n" +
                "    \"id\": \"envelope_123\",\n" +
                "    \"state\": \"draft\",\n" +
                "    \"name\": \"Test envelope\"\n" +
                "}";
        
        mockWebServer.enqueue(new MockResponse()
                .setResponseCode(200)
                .setHeader("Content-Type", "application/json")
                .setBody(envelopeResponse));
        
        // Test získania obálky
        JsonNode result = client.getEnvelope("envelope_123");
        
        assertEquals("envelope_123", result.get("id").asText());
        assertEquals("draft", result.get("state").asText());
        
        // Preskočíme auth request a overíme envelope request
        mockWebServer.takeRequest(); // auth request
        RecordedRequest envelopeRequest = mockWebServer.takeRequest();
        assertEquals("GET", envelopeRequest.getMethod());
        assertEquals("/api/envelope/envelope_123", envelopeRequest.getPath());
        assertTrue(envelopeRequest.getHeader("Authorization").startsWith("Bearer"));
    }
    
    @Test
    void testGetEnvelopeNotFound() throws Exception {
        // Mock autentifikácie
        mockWebServer.enqueue(new MockResponse()
                .setResponseCode(200)
                .setBody("{\"access_token\": \"test_token\", \"expires_in\": 3600}"));
        
        // Mock 404 odpovede
        mockWebServer.enqueue(new MockResponse()
                .setResponseCode(404)
                .setHeader("Content-Type", "application/json")
                .setBody("{\"error\": {\"message\": \"Envelope not found\"}}"));
        
        // Test 404 chyby
        assertThrows(NotFoundError.class, () -> client.getEnvelope("nonexistent_id"));
    }
    
    @Test
    void testCreateEnvelopeSuccess() throws Exception {
        // Mock autentifikácie
        mockWebServer.enqueue(new MockResponse()
                .setResponseCode(200)
                .setBody("{\"access_token\": \"test_token\", \"expires_in\": 3600}"));
        
        // Mock úspešného vytvorenia obálky
        String createResponse = "{\n" +
                "    \"id\": \"envelope_456\",\n" +
                "    \"state\": \"ready\",\n" +
                "    \"expirationtime\": 1234567890000,\n" +
                "    \"processes\": [{\"id\": \"process_123\"}]\n" +
                "}";
        
        mockWebServer.enqueue(new MockResponse()
                .setResponseCode(200)
                .setHeader("Content-Type", "application/json")
                .setBody(createResponse));
        
        // Test vytvorenia obálky
        Map<String, byte[]> documentsData = new HashMap<>();
        documentsData.put("test.pdf", "fake_pdf_content".getBytes());
        
        JsonNode result = client.createEnvelope(
                "New envelope",
                "default",
                "ready",
                24,
                "https://example.com",
                "sk",
                "<EMAIL>",
                "Test User",
                "<EMAIL>",
                null,
                documentsData
        );
        
        assertEquals("envelope_456", result.get("id").asText());
        assertEquals("ready", result.get("state").asText());
        
        // Preskočíme auth request a overíme create request
        mockWebServer.takeRequest(); // auth request
        RecordedRequest createRequest = mockWebServer.takeRequest();
        assertEquals("POST", createRequest.getMethod());
        assertEquals("/api/envelope", createRequest.getPath());
        assertTrue(createRequest.getHeader("Authorization").startsWith("Bearer"));
        assertTrue(createRequest.getHeader("Content-Type").contains("multipart/form-data"));
    }
    
    @Test
    void testGetProcessInfoSuccess() throws Exception {
        // Mock autentifikácie
        mockWebServer.enqueue(new MockResponse()
                .setResponseCode(200)
                .setBody("{\"access_token\": \"test_token\", \"expires_in\": 3600}"));
        
        // Mock úspešného získania procesu
        String processResponse = "{\n" +
                "    \"id\": \"process_123\",\n" +
                "    \"envelopeid\": \"envelope_456\",\n" +
                "    \"state\": \"ready\",\n" +
                "    \"signer\": \"user1\",\n" +
                "    \"sigtype\": \"bio\"\n" +
                "}";
        
        mockWebServer.enqueue(new MockResponse()
                .setResponseCode(200)
                .setHeader("Content-Type", "application/json")
                .setBody(processResponse));
        
        // Test získania procesu
        JsonNode result = client.getProcessInfo("process_123");
        
        assertEquals("process_123", result.get("id").asText());
        assertEquals("envelope_456", result.get("envelopeid").asText());
        assertEquals("bio", result.get("sigtype").asText());
    }
}
