package sk.signatus.demo.exception;

import com.fasterxml.jackson.databind.JsonNode;

/**
 * Výjimka pre chyby typu 404 Not Found
 * 
 * T<PERSON>to výjimka sa vyhodí keď požadovaný zdroj (obálka, dokument, proces)
 * nebol nájdený na serveri.
 */
public class NotFoundError extends Signatus2Exception {
    
    /**
     * Konštruktor s iba správou
     * 
     * @param message chybová správa
     */
    public NotFoundError(String message) {
        super(message);
    }
    
    /**
     * Konštruktor s správou a príčinou
     * 
     * @param message chybová správa
     * @param cause príčina chyby
     */
    public NotFoundError(String message, Throwable cause) {
        super(message, cause);
    }
    
    /**
     * Kon<PERSON>truktor s kompletnou informáciou
     * 
     * @param message chybová správa
     * @param statusCode HTTP status kód
     * @param responseData response data z API
     */
    public NotFoundError(String message, Integer statusCode, JsonNode responseData) {
        super(message, statusCode, responseData);
    }
    
    /**
     * Konštruktor s kompletnou informáciou a príčinou
     * 
     * @param message chybová správa
     * @param cause príčina chyby
     * @param statusCode HTTP status kód
     * @param responseData response data z API
     */
    public NotFoundError(String message, Throwable cause, Integer statusCode, JsonNode responseData) {
        super(message, cause, statusCode, responseData);
    }
}
