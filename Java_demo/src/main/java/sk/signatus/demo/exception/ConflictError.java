package sk.signatus.demo.exception;

import com.fasterxml.jackson.databind.JsonNode;

/**
 * Výjimka pre chyby typu 409 Conflict
 * 
 * Táto výjimka sa vyhodí pri konfliktoch v požiadavkách,
 * napríklad pri pokuse o vytvorenie duplicitného z<PERSON>namu.
 */
public class ConflictError extends Signatus2Exception {
    
    /**
     * Konštruktor s iba správou
     * 
     * @param message chybová správa
     */
    public ConflictError(String message) {
        super(message);
    }
    
    /**
     * Konštruktor s správou a príčinou
     * 
     * @param message chybová správa
     * @param cause príčina chyby
     */
    public ConflictError(String message, Throwable cause) {
        super(message, cause);
    }
    
    /**
     * Konštruktor s kompletnou informáciou
     * 
     * @param message chybová správa
     * @param statusCode HTTP status kód
     * @param responseData response data z API
     */
    public ConflictError(String message, Integer statusCode, JsonNode responseData) {
        super(message, statusCode, responseData);
    }
    
    /**
     * Kon<PERSON>truktor s kompletnou informáciou a príčinou
     * 
     * @param message chybová správa
     * @param cause príčina chyby
     * @param statusCode HTTP status kód
     * @param responseData response data z API
     */
    public ConflictError(String message, Throwable cause, Integer statusCode, JsonNode responseData) {
        super(message, cause, statusCode, responseData);
    }
}
