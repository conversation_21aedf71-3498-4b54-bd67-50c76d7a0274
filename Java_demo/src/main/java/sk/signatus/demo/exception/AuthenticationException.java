package sk.signatus.demo.exception;

import com.fasterxml.jackson.databind.JsonNode;

/**
 * Výjimka pre chyby autentifikácie
 * 
 * Táto výjimka sa vyhodí pri problémoch s OAuth autentifikáciou,
 * typicky pri HTTP 401 Unauthorized odpovediach.
 */
public class AuthenticationException extends Signatus2Exception {
    
    /**
     * Konštruktor s iba správou
     * 
     * @param message chybová správa
     */
    public AuthenticationException(String message) {
        super(message);
    }
    
    /**
     * Konštruktor s správou a príčinou
     * 
     * @param message chybová správa
     * @param cause príčina chyby
     */
    public AuthenticationException(String message, Throwable cause) {
        super(message, cause);
    }
    
    /**
     * Konštruktor s kompletnou informáciou
     * 
     * @param message chybová správa
     * @param statusCode HTTP status kód
     * @param responseData response data z API
     */
    public AuthenticationException(String message, Integer statusCode, JsonNode responseData) {
        super(message, statusCode, responseData);
    }
    
    /**
     * Kon<PERSON>truktor s kompletnou informáciou a príčinou
     * 
     * @param message chybová správa
     * @param cause príčina chyby
     * @param statusCode HTTP status kód
     * @param responseData response data z API
     */
    public AuthenticationException(String message, Throwable cause, Integer statusCode, JsonNode responseData) {
        super(message, cause, statusCode, responseData);
    }
}
