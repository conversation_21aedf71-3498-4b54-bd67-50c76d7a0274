package sk.signatus.demo.exception;

import com.fasterxml.jackson.databind.JsonNode;

/**
 * Základná výjimka pre Signatus2 API chyby
 * 
 * T<PERSON><PERSON> trieda reprezentuje všeobecnú chybu pri komunikácii so Signatus2 API.
 * Obsahuje dodatočné informácie ako HTTP status kód a response data.
 */
public class Signatus2Exception extends Exception {
    
    private final Integer statusCode;
    private final JsonNode responseData;
    
    /**
     * Konštruktor s iba správou
     * 
     * @param message chybová správa
     */
    public Signatus2Exception(String message) {
        super(message);
        this.statusCode = null;
        this.responseData = null;
    }
    
    /**
     * Konštruktor s správou a príčinou
     * 
     * @param message chybová správa
     * @param cause príčina chyby
     */
    public Signatus2Exception(String message, Throwable cause) {
        super(message, cause);
        this.statusCode = null;
        this.responseData = null;
    }
    
    /**
     * <PERSON>n<PERSON><PERSON><PERSON>tor s kompletnou informáciou
     * 
     * @param message chybová správa
     * @param statusCode HTTP status kód
     * @param responseData response data z API
     */
    public Signatus2Exception(String message, Integer statusCode, JsonNode responseData) {
        super(message);
        this.statusCode = statusCode;
        this.responseData = responseData;
    }
    
    /**
     * Konštruktor s kompletnou informáciou a príčinou
     * 
     * @param message chybová správa
     * @param cause príčina chyby
     * @param statusCode HTTP status kód
     * @param responseData response data z API
     */
    public Signatus2Exception(String message, Throwable cause, Integer statusCode, JsonNode responseData) {
        super(message, cause);
        this.statusCode = statusCode;
        this.responseData = responseData;
    }
    
    /**
     * Získanie HTTP status kódu
     * 
     * @return HTTP status kód alebo null ak nie je dostupný
     */
    public Integer getStatusCode() {
        return statusCode;
    }
    
    /**
     * Získanie response data z API
     * 
     * @return JSON response data alebo null ak nie sú dostupné
     */
    public JsonNode getResponseData() {
        return responseData;
    }
    
    /**
     * Kontrola či má výjimka HTTP status kód
     * 
     * @return true ak má status kód, false inak
     */
    public boolean hasStatusCode() {
        return statusCode != null;
    }
    
    /**
     * Kontrola či má výjimka response data
     * 
     * @return true ak má response data, false inak
     */
    public boolean hasResponseData() {
        return responseData != null;
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName()).append(": ").append(getMessage());
        
        if (hasStatusCode()) {
            sb.append(" (HTTP ").append(statusCode).append(")");
        }
        
        if (hasResponseData()) {
            sb.append(" - Response: ").append(responseData.toString());
        }
        
        return sb.toString();
    }
}
