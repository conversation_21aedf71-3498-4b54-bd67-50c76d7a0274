package sk.signatus.demo;

import com.fasterxml.jackson.databind.JsonNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import sk.signatus.demo.exception.*;
import sk.signatus.demo.util.PdfUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * Hlavná demo trieda pre Signatus2 API
 * 
 * Demonštruje kompletný workflow:
 * 1. Autentifikácia
 * 2. Vytvorenie obálky s dokumentom
 * 3. Testovanie nových API endpointov
 * 4. Získanie informácií o obálke a procese
 */
public class Main {
    
    private static final Logger logger = LoggerFactory.getLogger(Main.class);
    
    public static void main(String[] args) {
        logger.info("=== Signatus2 API Demo (Java) ===");
        
        try {
            runDemo();
        } catch (Exception e) {
            logger.error("❌ Neočakávaná chyba: {}", e.getMessage(), e);
            System.exit(1);
        }
    }
    
    /**
     * <PERSON><PERSON><PERSON> demo funkcia
     */
    private static void runDemo() {
        try (Signatus2Client client = new Signatus2Client()) {
            
            // 1. Autentifikácia
            logger.info("1. Autentifikácia...");
            String token = client.authenticate();
            logger.info("✓ Získaný access token: {}...", token.substring(0, Math.min(20, token.length())));
            
            // 2. Vytvorenie obálky s dokumentom
            logger.info("\n2. Vytvorenie obálky s dokumentom...");
            
            // Príprava dokumentu
            String pdfContent = PdfUtils.loadSamplePdf();
            Map<String, byte[]> documentsData = new HashMap<>();
            documentsData.put("demo_document.pdf", PdfUtils.base64ToBytes(pdfContent));
            
            JsonNode envelope = client.createEnvelope(
                    "Demo obálka - Java API",
                    "default",
                    "ready",
                    24,
                    "https://www.signatus.com",
                    "sk",
                    "<EMAIL>",
                    "Demo Používateľ Java",
                    "<EMAIL>",
                    null, // použije predvolené procesy
                    documentsData
            );
            
            String envelopeId = envelope.has("id") ? envelope.get("id").asText() : null;
            String processId = null;
            
            if (envelope.has("processes") && envelope.get("processes").isArray() && 
                envelope.get("processes").size() > 0) {
                JsonNode firstProcess = envelope.get("processes").get(0);
                if (firstProcess.has("id")) {
                    processId = firstProcess.get("id").asText();
                }
            }
            
            logger.info("✓ Vytvorená obálka s ID: {}", envelopeId);
            if (processId != null) {
                logger.info("✓ Proces ID: {}", processId);
            }
            
            // 3. Získanie informácií o procese (ak existuje)
            if (processId != null) {
                logger.info("\n3. Získanie informácií o procese...");
                try {
                    JsonNode processInfo = client.getProcessInfo(processId);
                    String processState = processInfo.has("state") ? processInfo.get("state").asText() : "neznámy";
                    logger.info("✓ Stav procesu: {}", processState);
                    
                    // Získanie document ID z procesu
                    if (processInfo.has("documentsProcess") && processInfo.get("documentsProcess").isArray()) {
                        JsonNode documentsProcess = processInfo.get("documentsProcess");
                        if (documentsProcess.size() > 0) {
                            JsonNode firstDoc = documentsProcess.get(0);
                            if (firstDoc.has("documentid")) {
                                String documentId = firstDoc.get("documentid").asText();
                                logger.info("✓ Dokument ID: {}", documentId);
                            }
                        }
                    }
                } catch (Exception e) {
                    logger.warn("⚠️ Chyba pri získavaní informácií o procese: {}", e.getMessage());
                }
            }
            
            // 4. Testovanie nových endpointov
            logger.info("\n4. Testovanie nových endpointov...");
            try {
                // Kompletné informácie o obálke
                JsonNode completeInfo = client.getEnvelopeComplete(envelopeId);
                String state = completeInfo.has("state") ? completeInfo.get("state").asText() : "neznámy";
                logger.info("✓ Kompletné info - stav: {}", state);
                
                // Dáta obálky
                String envelopeData = client.getEnvelopeData(envelopeId);
                logger.info("✓ Dáta obálky získané (dĺžka: {} znakov)", envelopeData.length());
                
            } catch (Exception e) {
                logger.warn("⚠️ Chyba pri testovaní nových endpointov: {}", e.getMessage());
            }
            
            // 5. Získanie základných informácií o obálke
            logger.info("\n5. Získanie základných informácií o obálke...");
            JsonNode envelopeInfo = client.getEnvelope(envelopeId);
            String envelopeState = envelopeInfo.has("state") ? envelopeInfo.get("state").asText() : "neznámy";
            logger.info("✓ Stav obálky: {}", envelopeState);
            
            // 6. Test vymazania obálky (voliteľné - odkomentujte ak chcete skutočne vymazať)
            // logger.info("\n6. Vymazanie obálky...");
            // JsonNode deleteResult = client.deleteEnvelope(envelopeId);
            // String message = deleteResult.has("message") ? deleteResult.get("message").asText() : "OK";
            // logger.info("✓ Obálka vymazaná: {}", message);
            
            logger.info("\n=== Demo úspešne dokončené! ===");
            logger.info("💡 Tip: Skúste spustiť s rôznymi parametrami alebo rozšírte funkcionalitu");
            
        } catch (AuthenticationException e) {
            logger.error("❌ Chyba autentifikácie: {}", e.getMessage());
            logger.error("Skontrolujte prihlasovacie údaje v konfigurácii");
        } catch (NotFoundError e) {
            logger.error("❌ Zdroj nebol nájdený: {}", e.getMessage());
        } catch (ConflictError e) {
            logger.error("❌ Konflikt: {}", e.getMessage());
        } catch (Signatus2Exception e) {
            logger.error("❌ API chyba: {}", e.getMessage());
            if (e.hasResponseData()) {
                logger.error("Detaily: {}", e.getResponseData().toString());
            }
        } catch (Exception e) {
            logger.error("❌ Neočakávaná chyba: {}", e.getMessage(), e);
            throw e;
        }
    }
}
