package sk.signatus.demo;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import sk.signatus.demo.exception.*;
import sk.signatus.demo.model.EnvelopeRequest;
import sk.signatus.demo.model.TokenResponse;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * Klient pre komunikáciu so Signatus2 API
 * 
 * Poskytuje metódy pre OAuth autentifikáciu a správu obálok/dokumentov.
 * Implementuje všetky hlavné API endpointy s proper error handling.
 */
public class Signatus2Client implements AutoCloseable {
    
    private static final Logger logger = LoggerFactory.getLogger(Signatus2Client.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    private final OkHttpClient httpClient;
    private String accessToken;
    private LocalDateTime tokenExpiresAt;
    
    /**
     * Konštruktor s predvoleným HTTP klientom
     */
    public Signatus2Client() {
        this.httpClient = createHttpClient();
    }
    
    /**
     * Konštruktor s vlastným HTTP klientom
     * 
     * @param httpClient vlastný HTTP klient
     */
    public Signatus2Client(OkHttpClient httpClient) {
        this.httpClient = httpClient;
    }
    
    /**
     * Vytvorenie predvoleného HTTP klienta s timeoutmi
     */
    private OkHttpClient createHttpClient() {
        return new OkHttpClient.Builder()
                .connectTimeout(Config.REQUEST_TIMEOUT, TimeUnit.SECONDS)
                .readTimeout(Config.REQUEST_TIMEOUT, TimeUnit.SECONDS)
                .writeTimeout(Config.REQUEST_TIMEOUT, TimeUnit.SECONDS)
                .build();
    }
    
    /**
     * OAuth autentifikácia - získanie access tokenu
     * 
     * @return access token
     * @throws AuthenticationException pri chybe autentifikácie
     * @throws Signatus2Exception pri iných API chybách
     */
    public String authenticate() throws AuthenticationException, Signatus2Exception {
        logger.info("Začínam OAuth autentifikáciu...");
        
        // Vytvorenie Basic Auth hlavičky
        String credentials = Config.CLIENT_ID + ":" + Config.CLIENT_SECRET;
        String basicAuth = "Basic " + Base64.getEncoder().encodeToString(credentials.getBytes(StandardCharsets.UTF_8));
        
        // Príprava form data
        FormBody formBody = new FormBody.Builder()
                .add("grant_type", "password")
                .add("username", Config.USERNAME)
                .add("password", Config.PASSWORD)
                .add("scope", "openid")
                .build();
        
        Request request = new Request.Builder()
                .url(Config.AUTH_URL)
                .header("Authorization", basicAuth)
                .header("Content-Type", "application/x-www-form-urlencoded")
                .post(formBody)
                .build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            JsonNode responseData = handleResponse(response);
            
            TokenResponse tokenResponse = objectMapper.treeToValue(responseData, TokenResponse.class);
            this.accessToken = tokenResponse.getAccessToken();
            
            // Nastavenie času expirácie s 60s bufferom
            int expiresIn = tokenResponse.getExpiresIn() != null ? tokenResponse.getExpiresIn() : 3600;
            this.tokenExpiresAt = LocalDateTime.now().plusSeconds(expiresIn - 60);
            
            logger.info("OAuth autentifikácia úspešná");
            return this.accessToken;
            
        } catch (IOException e) {
            throw new AuthenticationException("Chyba pri autentifikácii: " + e.getMessage(), e);
        }
    }
    
    /**
     * Zabezpečenie platnej autentifikácie
     * Automaticky obnoví token ak je potrebné
     */
    private void ensureAuthenticated() throws AuthenticationException, Signatus2Exception {
        if (accessToken == null || (tokenExpiresAt != null && LocalDateTime.now().isAfter(tokenExpiresAt))) {
            authenticate();
        }
    }
    
    /**
     * Získanie autentifikačných hlavičiek
     */
    private Headers.Builder getAuthHeaders() throws AuthenticationException, Signatus2Exception {
        ensureAuthenticated();
        return new Headers.Builder()
                .add("Authorization", "Bearer " + accessToken)
                .add("Content-Type", "application/json");
    }
    
    /**
     * Spracovanie HTTP odpovede a error handling
     * 
     * @param response HTTP odpoveď
     * @return parsovaná JSON odpoveď
     * @throws Signatus2Exception pri chybách API
     */
    private JsonNode handleResponse(Response response) throws Signatus2Exception {
        try {
            String responseBody = response.body() != null ? response.body().string() : "";
            JsonNode responseData = null;
            
            if (!responseBody.isEmpty()) {
                try {
                    responseData = objectMapper.readTree(responseBody);
                } catch (Exception e) {
                    // Ak nie je JSON, vytvoríme objekt s raw response
                    responseData = objectMapper.createObjectNode().put("raw_response", responseBody);
                }
            }
            
            // Spracovanie rôznych HTTP status kódov
            if (response.isSuccessful()) {
                return responseData;
            } else if (response.code() == 401) {
                throw new AuthenticationException(
                    "Neplatná autentifikácia - skontrolujte prihlasovacie údaje",
                    response.code(),
                    responseData
                );
            } else if (response.code() == 404) {
                throw new NotFoundError(
                    "Zdroj nebol nájdený",
                    response.code(),
                    responseData
                );
            } else if (response.code() == 409) {
                throw new ConflictError(
                    "Konflikt pri spracovaní požiadavky",
                    response.code(),
                    responseData
                );
            } else {
                String errorMessage = "HTTP " + response.code();
                if (responseData != null && responseData.has("error")) {
                    JsonNode error = responseData.get("error");
                    if (error.has("message")) {
                        errorMessage = error.get("message").asText();
                    }
                }
                throw new Signatus2Exception(
                    "API chyba: " + errorMessage,
                    response.code(),
                    responseData
                );
            }
        } catch (IOException e) {
            throw new Signatus2Exception("Chyba pri spracovaní odpovede: " + e.getMessage(), e);
        }
    }
    
    /**
     * Získanie informácií o obálke
     *
     * @param envelopeId ID obálky
     * @return informácie o obálke
     * @throws Signatus2Exception pri chybách API
     */
    public JsonNode getEnvelope(String envelopeId) throws Signatus2Exception {
        logger.info("Získavam informácie o obálke {}", envelopeId);

        String url = Config.API_BASE_URL + "/envelope/" + envelopeId;

        Request request = new Request.Builder()
                .url(url)
                .headers(getAuthHeaders()
                        .set("Content-Type", "application/x-www-form-urlencoded")
                        .build())
                .get()
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            return handleResponse(response);
        } catch (IOException e) {
            throw new Signatus2Exception("Chyba pri získavaní obálky: " + e.getMessage(), e);
        }
    }

    /**
     * Získanie kompletných informácií o obálke
     *
     * @param envelopeId ID obálky
     * @return kompletné informácie o obálke
     * @throws Signatus2Exception pri chybách API
     */
    public JsonNode getEnvelopeComplete(String envelopeId) throws Signatus2Exception {
        logger.info("Získavam kompletné informácie o obálke {}", envelopeId);

        String url = Config.API_BASE_URL + "/envelope/complete/" + envelopeId;

        Request request = new Request.Builder()
                .url(url)
                .headers(getAuthHeaders()
                        .set("Content-Type", "application/x-www-form-urlencoded")
                        .build())
                .get()
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            return handleResponse(response);
        } catch (IOException e) {
            throw new Signatus2Exception("Chyba pri získavaní kompletných informácií o obálke: " + e.getMessage(), e);
        }
    }

    /**
     * Získanie dát obálky (raw data)
     *
     * @param envelopeId ID obálky
     * @return raw dáta obálky
     * @throws Signatus2Exception pri chybách API
     */
    public String getEnvelopeData(String envelopeId) throws Signatus2Exception {
        logger.info("Získavam dáta obálky {}", envelopeId);

        String url = Config.API_BASE_URL + "/envelopeData/" + envelopeId;

        Request request = new Request.Builder()
                .url(url)
                .headers(getAuthHeaders()
                        .set("Content-Type", "application/x-www-form-urlencoded")
                        .build())
                .get()
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (response.isSuccessful()) {
                return response.body() != null ? response.body().string() : "";
            } else {
                handleResponse(response); // Vyhodí výjimku
                return null; // Nikdy sa nedosiahne
            }
        } catch (IOException e) {
            throw new Signatus2Exception("Chyba pri získavaní dát obálky: " + e.getMessage(), e);
        }
    }

    /**
     * Vymazanie obálky
     *
     * @param envelopeId ID obálky
     * @return výsledok vymazania
     * @throws Signatus2Exception pri chybách API
     */
    public JsonNode deleteEnvelope(String envelopeId) throws Signatus2Exception {
        logger.info("Mažem obálku {}", envelopeId);

        String url = Config.API_BASE_URL + "/envelope/" + envelopeId;

        Request request = new Request.Builder()
                .url(url)
                .headers(getAuthHeaders()
                        .set("Content-Type", "application/x-www-form-urlencoded")
                        .build())
                .delete()
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            JsonNode result = handleResponse(response);
            logger.info("Obálka úspešne vymazaná");
            return result;
        } catch (IOException e) {
            throw new Signatus2Exception("Chyba pri mazaní obálky: " + e.getMessage(), e);
        }
    }

    /**
     * Získanie informácií o procese
     *
     * @param processId ID procesu
     * @return informácie o procese
     * @throws Signatus2Exception pri chybách API
     */
    public JsonNode getProcessInfo(String processId) throws Signatus2Exception {
        logger.info("Získavam informácie o procese {}", processId);

        String url = Config.API_BASE_URL + "/process/" + processId;

        Request request = new Request.Builder()
                .url(url)
                .headers(getAuthHeaders().build())
                .get()
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            return handleResponse(response);
        } catch (IOException e) {
            throw new Signatus2Exception("Chyba pri získavaní informácií o procese: " + e.getMessage(), e);
        }
    }

    /**
     * Vytvorenie novej obálky podľa novej API štruktúry
     *
     * @param name názov obálky
     * @param envelopeType typ obálky (default)
     * @param state stav obálky (ready)
     * @param expirationHours počet hodín do expirácie
     * @param resultUrl URL pre presmerovanie
     * @param lang jazyk (sk/en)
     * @param emailFrom email odosielateľa
     * @param nameFrom meno odosielateľa
     * @param emailTo email príjemcu
     * @param processes zoznam procesov (voliteľné)
     * @param documentsData mapa s názvami súborov a ich obsahom (voliteľné)
     * @return informácie o vytvorenej obálke
     * @throws Signatus2Exception pri chybách API
     */
    public JsonNode createEnvelope(
            String name,
            String envelopeType,
            String state,
            int expirationHours,
            String resultUrl,
            String lang,
            String emailFrom,
            String nameFrom,
            String emailTo,
            List<EnvelopeRequest.Process> processes,
            Map<String, byte[]> documentsData
    ) throws Signatus2Exception {

        logger.info("Vytváram novú obálku: {}", name);

        String url = Config.API_BASE_URL + "/envelope";

        // Výpočet času expirácie
        long expirationTimestamp = LocalDateTime.now()
                .plusHours(expirationHours)
                .toEpochSecond(ZoneOffset.UTC) * 1000;

        // Predvolené procesy ak nie sú zadané
        if (processes == null || processes.isEmpty()) {
            EnvelopeRequest.Document document = new EnvelopeRequest.Document("write", "demo_document.pdf");
            EnvelopeRequest.Process process = new EnvelopeRequest.Process(
                    "user1", "document", "bio", Collections.singletonList(document)
            );
            processes = Collections.singletonList(process);
        }

        // Vytvorenie dataText JSON štruktúry
        EnvelopeRequest envelopeRequest = new EnvelopeRequest();
        envelopeRequest.setEnvelopeType(envelopeType);
        envelopeRequest.setState(state);
        envelopeRequest.setType("menu1");
        envelopeRequest.setExpirationTime(expirationTimestamp);
        envelopeRequest.setRules(new EnvelopeRequest.Rules(resultUrl, lang, emailTo, emailFrom, nameFrom));
        envelopeRequest.setProcesses(processes);
        envelopeRequest.setExtension(new HashMap<>());

        try {
            // Príprava multipart form data
            MultipartBody.Builder multipartBuilder = new MultipartBody.Builder()
                    .setType(MultipartBody.FORM)
                    .addFormDataPart("dataText", objectMapper.writeValueAsString(envelopeRequest));

            // Pridanie dokumentov ak sú poskytnuté
            if (documentsData != null) {
                for (Map.Entry<String, byte[]> entry : documentsData.entrySet()) {
                    RequestBody fileBody = RequestBody.create(entry.getValue(), MediaType.parse("application/pdf"));
                    multipartBuilder.addFormDataPart(entry.getKey(), entry.getKey(), fileBody);
                }
            }

            RequestBody requestBody = multipartBuilder.build();

            Request request = new Request.Builder()
                    .url(url)
                    .header("Authorization", "Bearer " + accessToken)
                    .header("CSRF-SIGNATUS-TOKEN", Config.CSRF_TOKEN)
                    .post(requestBody)
                    .build();

            ensureAuthenticated();

            try (Response response = httpClient.newCall(request).execute()) {
                JsonNode result = handleResponse(response);
                logger.info("Obálka vytvorená s ID: {}", result.has("id") ? result.get("id").asText() : "unknown");
                return result;
            }

        } catch (IOException e) {
            throw new Signatus2Exception("Chyba pri vytváraní obálky: " + e.getMessage(), e);
        }
    }

    /**
     * Convenience metóda pre vytvorenie obálky s predvolenými parametrami
     *
     * @param name názov obálky
     * @param emailTo email príjemcu
     * @param emailFrom email odosielateľa
     * @param nameFrom meno odosielateľa
     * @param documentsData mapa s dokumentmi
     * @return informácie o vytvorenej obálke
     * @throws Signatus2Exception pri chybách API
     */
    public JsonNode createEnvelope(
            String name,
            String emailTo,
            String emailFrom,
            String nameFrom,
            Map<String, byte[]> documentsData
    ) throws Signatus2Exception {
        return createEnvelope(
                name,
                "default",                    // predvolený typ obálky
                "ready",                      // predvolený stav
                24,                          // 24 hodín expirácia
                "https://www.signatus.com",  // predvolená result URL
                "sk",                        // slovenský jazyk
                emailFrom,
                nameFrom,
                emailTo,
                null,                        // predvolené procesy
                documentsData
        );
    }

    /**
     * Convenience metóda pre vytvorenie obálky iba s názvom a emailmi
     *
     * @param name názov obálky
     * @param emailTo email príjemcu
     * @param emailFrom email odosielateľa
     * @param nameFrom meno odosielateľa
     * @return informácie o vytvorenej obálke
     * @throws Signatus2Exception pri chybách API
     */
    public JsonNode createEnvelope(
            String name,
            String emailTo,
            String emailFrom,
            String nameFrom
    ) throws Signatus2Exception {
        return createEnvelope(name, emailTo, emailFrom, nameFrom, null);
    }

    /**
     * Získanie informácií o konfigurácii
     *
     * @return string s informáciami o konfigurácii
     */
    public String getConfigInfo() {
        return Config.getConfigInfo();
    }

    /**
     * Kontrola či je klient autentifikovaný
     *
     * @return true ak má platný token, false inak
     */
    public boolean isAuthenticated() {
        return accessToken != null &&
               (tokenExpiresAt == null || LocalDateTime.now().isBefore(tokenExpiresAt));
    }

    /**
     * Získanie aktuálneho access tokenu (bez automatickej obnovy)
     *
     * @return access token alebo null ak nie je autentifikovaný
     */
    public String getCurrentToken() {
        return accessToken;
    }

    @Override
    public void close() {
        // OkHttpClient sa automaticky zatvorí
        logger.debug("Signatus2Client zatvorený");
    }
}
