package sk.signatus.demo.util;

import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * Utility trieda pre prácu s PDF súbormi
 * 
 * Poskytuje metódy pre vytvorenie vzorových PDF súborov a ich konverziu.
 */
public class PdfUtils {
    
    /**
     * Načítanie vzorového PDF súboru a konverzia do Base64
     * 
     * @return Base64 kódovaný obsah PDF
     */
    public static String loadSamplePdf() {
        // Jednoduchý PDF obsah pre demo účely
        String samplePdf = "%PDF-1.4\n" +
                "1 0 obj\n" +
                "<<\n" +
                "/Type /Catalog\n" +
                "/Pages 2 0 R\n" +
                ">>\n" +
                "endobj\n" +
                "\n" +
                "2 0 obj\n" +
                "<<\n" +
                "/Type /Pages\n" +
                "/Kids [3 0 R]\n" +
                "/Count 1\n" +
                ">>\n" +
                "endobj\n" +
                "\n" +
                "3 0 obj\n" +
                "<<\n" +
                "/Type /Page\n" +
                "/Parent 2 0 R\n" +
                "/MediaBox [0 0 612 792]\n" +
                "/Contents 4 0 R\n" +
                ">>\n" +
                "endobj\n" +
                "\n" +
                "4 0 obj\n" +
                "<<\n" +
                "/Length 44\n" +
                ">>\n" +
                "stream\n" +
                "BT\n" +
                "/F1 12 Tf\n" +
                "72 720 Td\n" +
                "(Hello, Signatus2!) Tj\n" +
                "ET\n" +
                "endstream\n" +
                "endobj\n" +
                "\n" +
                "xref\n" +
                "0 5\n" +
                "0000000000 65535 f \n" +
                "0000000009 00000 n \n" +
                "0000000058 00000 n \n" +
                "0000000115 00000 n \n" +
                "0000000206 00000 n \n" +
                "trailer\n" +
                "<<\n" +
                "/Size 5\n" +
                "/Root 1 0 R\n" +
                ">>\n" +
                "startxref\n" +
                "299\n" +
                "%%EOF";
        
        return Base64.getEncoder().encodeToString(samplePdf.getBytes(StandardCharsets.UTF_8));
    }
    
    /**
     * Konverzia Base64 stringu na byte array
     * 
     * @param base64Content Base64 kódovaný obsah
     * @return byte array s dekódovaným obsahom
     */
    public static byte[] base64ToBytes(String base64Content) {
        return Base64.getDecoder().decode(base64Content);
    }
    
    /**
     * Konverzia byte array na Base64 string
     * 
     * @param content byte array s obsahom
     * @return Base64 kódovaný string
     */
    public static String bytesToBase64(byte[] content) {
        return Base64.getEncoder().encodeToString(content);
    }
    
    // Privátny konštruktor - utility trieda
    private PdfUtils() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }
}
