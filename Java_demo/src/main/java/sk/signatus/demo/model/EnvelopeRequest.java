package sk.signatus.demo.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.util.Map;

/**
 * Model pre vytvorenie obálky
 * 
 * Reprezentuje štruktúru dataText JSON objektu pre vytvorenie obálky.
 */
public class EnvelopeRequest {
    
    @JsonProperty("envelopetype")
    private String envelopeType;
    
    @JsonProperty("state")
    private String state;
    
    @JsonProperty("type")
    private String type;
    
    @JsonProperty("expirationtime")
    private Long expirationTime;
    
    @JsonProperty("rules")
    private Rules rules;
    
    @JsonProperty("processes")
    private List<Process> processes;
    
    @JsonProperty("extension")
    private Map<String, Object> extension;
    
    // Vnorené triedy
    public static class Rules {
        @JsonProperty("resulturl")
        private String resultUrl;
        
        @JsonProperty("lang")
        private String lang;
        
        @JsonProperty("welcomeScreen")
        private String welcomeScreen;
        
        @JsonProperty("emailFinal")
        private String emailFinal;
        
        @JsonProperty("emailTo")
        private String emailTo;
        
        @JsonProperty("emailFrom")
        private String emailFrom;
        
        @JsonProperty("nameFrom")
        private String nameFrom;
        
        // Konštruktory
        public Rules() {}
        
        public Rules(String resultUrl, String lang, String emailTo, String emailFrom, String nameFrom) {
            this.resultUrl = resultUrl;
            this.lang = lang;
            this.welcomeScreen = "none";
            this.emailFinal = emailFrom + "; " + emailTo;
            this.emailTo = emailTo;
            this.emailFrom = emailFrom;
            this.nameFrom = nameFrom;
        }
        
        // Getters a Setters
        public String getResultUrl() { return resultUrl; }
        public void setResultUrl(String resultUrl) { this.resultUrl = resultUrl; }
        
        public String getLang() { return lang; }
        public void setLang(String lang) { this.lang = lang; }
        
        public String getWelcomeScreen() { return welcomeScreen; }
        public void setWelcomeScreen(String welcomeScreen) { this.welcomeScreen = welcomeScreen; }
        
        public String getEmailFinal() { return emailFinal; }
        public void setEmailFinal(String emailFinal) { this.emailFinal = emailFinal; }
        
        public String getEmailTo() { return emailTo; }
        public void setEmailTo(String emailTo) { this.emailTo = emailTo; }
        
        public String getEmailFrom() { return emailFrom; }
        public void setEmailFrom(String emailFrom) { this.emailFrom = emailFrom; }
        
        public String getNameFrom() { return nameFrom; }
        public void setNameFrom(String nameFrom) { this.nameFrom = nameFrom; }
    }
    
    public static class Process {
        @JsonProperty("signer")
        private String signer;
        
        @JsonProperty("signing")
        private String signing;
        
        @JsonProperty("sigtype")
        private String sigType;
        
        @JsonProperty("documents")
        private List<Document> documents;
        
        // Konštruktory
        public Process() {}
        
        public Process(String signer, String signing, String sigType, List<Document> documents) {
            this.signer = signer;
            this.signing = signing;
            this.sigType = sigType;
            this.documents = documents;
        }
        
        // Getters a Setters
        public String getSigner() { return signer; }
        public void setSigner(String signer) { this.signer = signer; }
        
        public String getSigning() { return signing; }
        public void setSigning(String signing) { this.signing = signing; }
        
        public String getSigType() { return sigType; }
        public void setSigType(String sigType) { this.sigType = sigType; }
        
        public List<Document> getDocuments() { return documents; }
        public void setDocuments(List<Document> documents) { this.documents = documents; }
    }
    
    public static class Document {
        @JsonProperty("method")
        private String method;
        
        @JsonProperty("reference")
        private String reference;
        
        // Konštruktory
        public Document() {}
        
        public Document(String method, String reference) {
            this.method = method;
            this.reference = reference;
        }
        
        // Getters a Setters
        public String getMethod() { return method; }
        public void setMethod(String method) { this.method = method; }
        
        public String getReference() { return reference; }
        public void setReference(String reference) { this.reference = reference; }
    }
    
    // Konštruktory
    public EnvelopeRequest() {}
    
    // Getters a Setters
    public String getEnvelopeType() { return envelopeType; }
    public void setEnvelopeType(String envelopeType) { this.envelopeType = envelopeType; }
    
    public String getState() { return state; }
    public void setState(String state) { this.state = state; }
    
    public String getType() { return type; }
    public void setType(String type) { this.type = type; }
    
    public Long getExpirationTime() { return expirationTime; }
    public void setExpirationTime(Long expirationTime) { this.expirationTime = expirationTime; }
    
    public Rules getRules() { return rules; }
    public void setRules(Rules rules) { this.rules = rules; }
    
    public List<Process> getProcesses() { return processes; }
    public void setProcesses(List<Process> processes) { this.processes = processes; }
    
    public Map<String, Object> getExtension() { return extension; }
    public void setExtension(Map<String, Object> extension) { this.extension = extension; }
}
