package sk.signatus.demo;

/**
 * Konfiguračné nastavenia pre Signatus2 API
 * 
 * T<PERSON>to trieda obsahuje všetky konfiguračné konštanty potrebné pre komunikáciu
 * so Signatus2 API, vr<PERSON>tane URL adries, OAuth credentials a ďalš<PERSON>ch nastavení.
 * 
 * Konfigu<PERSON><PERSON><PERSON> môže byť upravená pomocou environment variables.
 */
public class Config {
    
    // URL adresy - AKTUALIZOVANÉ podľa novej Postman kolekcie
    public static final String AUTH_URL = getEnvOrDefault(
        "SIGNATUS_AUTH_URL", 
        "https://dsoauth.ana.sk/realms/Signatus/protocol/openid-connect/token"
    );
    
    public static final String API_BASE_URL = getEnvOrDefault(
        "SIGNATUS_API_URL", 
        "https://ds-sts.ana.sk/signatus/api"
    );
    
    // OAuth credentials
    public static final String CLIENT_ID = getEnvOrDefault(
        "SIGNATUS_CLIENT_ID", 
        "signatus"
    );
    
    public static final String CLIENT_SECRET = getEnvOrDefault(
        "SIGNATUS_CLIENT_SECRET", 
        "fb5VXRnMZ2IbAIlik7uBS1mIZE2PJdBN"
    );
    
    // Používateľské údaje
    public static final String USERNAME = getEnvOrDefault(
        "SIGNATUS_USERNAME", 
        "iauser"
    );
    
    public static final String PASSWORD = getEnvOrDefault(
        "SIGNATUS_PASSWORD", 
        "S#DgfsH64"
    );
    
    // Ostatné nastavenia
    public static final int REQUEST_TIMEOUT = Integer.parseInt(
        getEnvOrDefault("SIGNATUS_TIMEOUT", "30")
    );
    
    public static final int MAX_RETRIES = Integer.parseInt(
        getEnvOrDefault("SIGNATUS_MAX_RETRIES", "3")
    );
    
    // CSRF token (voliteľný)
    public static final String CSRF_TOKEN = getEnvOrDefault(
        "SIGNATUS_CSRF_TOKEN", 
        "0bed913d-90bc-48be-ba95-55c8e63da864"
    );
    
    /**
     * Pomocná metóda pre získanie environment variable s fallback hodnotou
     * 
     * @param envName názov environment variable
     * @param defaultValue predvolená hodnota ak environment variable neexistuje
     * @return hodnota z environment alebo predvolená hodnota
     */
    private static String getEnvOrDefault(String envName, String defaultValue) {
        String value = System.getenv(envName);
        return value != null ? value : defaultValue;
    }
    
    /**
     * Zobrazenie aktuálnej konfigurácie (bez citlivých údajov)
     * 
     * @return string reprezentácia konfigurácie
     */
    public static String getConfigInfo() {
        return String.format(
            "Signatus2 API Configuration:\n" +
            "  Auth URL: %s\n" +
            "  API Base URL: %s\n" +
            "  Client ID: %s\n" +
            "  Username: %s\n" +
            "  Request Timeout: %d seconds\n" +
            "  Max Retries: %d",
            AUTH_URL,
            API_BASE_URL,
            CLIENT_ID,
            USERNAME,
            REQUEST_TIMEOUT,
            MAX_RETRIES
        );
    }
    
    // Privátny konštruktor - utility trieda
    private Config() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }
}
