-------------------------------------------------------------------------------
Test set: sk.signatus.demo.Signatus2ClientTest
-------------------------------------------------------------------------------
Tests run: 6, Failures: 3, Errors: 3, Skipped: 0, Time elapsed: 2.979 s <<< FAILURE! -- in sk.signatus.demo.Signatus2ClientTest
sk.signatus.demo.Signatus2ClientTest.testGetEnvelopeSuccess -- Time elapsed: 1.596 s <<< ERROR!
Signatus2Exception: API chyba: HTTP 500 (HTTP 500) - Response: {"message":"Invalid UUID string: envelope_123","value":""}
	at sk.signatus.demo.Signatus2Client.handleResponse(Signatus2Client.java:180)
	at sk.signatus.demo.Signatus2Client.getEnvelope(Signatus2Client.java:210)
	at sk.signatus.demo.Signatus2ClientTest.testGetEnvelopeSuccess(Signatus2ClientTest.java:113)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

sk.signatus.demo.Signatus2ClientTest.testGetEnvelopeNotFound -- Time elapsed: 0.329 s <<< FAILURE!
org.opentest4j.AssertionFailedError: Unexpected exception type thrown, expected: <sk.signatus.demo.exception.NotFoundError> but was: <sk.signatus.demo.exception.Signatus2Exception>
	at org.junit.jupiter.api.AssertionFailureBuilder.build(AssertionFailureBuilder.java:151)
	at org.junit.jupiter.api.AssertThrows.assertThrows(AssertThrows.java:67)
	at org.junit.jupiter.api.AssertThrows.assertThrows(AssertThrows.java:35)
	at org.junit.jupiter.api.Assertions.assertThrows(Assertions.java:3115)
	at sk.signatus.demo.Signatus2ClientTest.testGetEnvelopeNotFound(Signatus2ClientTest.java:140)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
Caused by: Signatus2Exception: API chyba: HTTP 500 (HTTP 500) - Response: {"message":"Invalid UUID string: nonexistent_id","value":""}
	at sk.signatus.demo.Signatus2Client.handleResponse(Signatus2Client.java:180)
	at sk.signatus.demo.Signatus2Client.getEnvelope(Signatus2Client.java:210)
	at sk.signatus.demo.Signatus2ClientTest.lambda$testGetEnvelopeNotFound$1(Signatus2ClientTest.java:140)
	at org.junit.jupiter.api.AssertThrows.assertThrows(AssertThrows.java:53)
	... 6 more

sk.signatus.demo.Signatus2ClientTest.testAuthenticationFailure -- Time elapsed: 0.227 s <<< FAILURE!
org.opentest4j.AssertionFailedError: Expected sk.signatus.demo.exception.AuthenticationException to be thrown, but nothing was thrown.
	at org.junit.jupiter.api.AssertionFailureBuilder.build(AssertionFailureBuilder.java:152)
	at org.junit.jupiter.api.AssertThrows.assertThrows(AssertThrows.java:73)
	at org.junit.jupiter.api.AssertThrows.assertThrows(AssertThrows.java:35)
	at org.junit.jupiter.api.Assertions.assertThrows(Assertions.java:3115)
	at sk.signatus.demo.Signatus2ClientTest.testAuthenticationFailure(Signatus2ClientTest.java:90)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

sk.signatus.demo.Signatus2ClientTest.testGetProcessInfoSuccess -- Time elapsed: 0.269 s <<< ERROR!
Signatus2Exception: API chyba: HTTP 500 (HTTP 500) - Response: {"message":"err.process.load","value":""}
	at sk.signatus.demo.Signatus2Client.handleResponse(Signatus2Client.java:180)
	at sk.signatus.demo.Signatus2Client.getProcessInfo(Signatus2Client.java:323)
	at sk.signatus.demo.Signatus2ClientTest.testGetProcessInfoSuccess(Signatus2ClientTest.java:215)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

sk.signatus.demo.Signatus2ClientTest.testCreateEnvelopeSuccess -- Time elapsed: 0.299 s <<< ERROR!
AuthenticationException: Neplatná autentifikácia - skontrolujte prihlasovacie údaje (HTTP 401)
	at sk.signatus.demo.Signatus2Client.handleResponse(Signatus2Client.java:155)
	at sk.signatus.demo.Signatus2Client.createEnvelope(Signatus2Client.java:414)
	at sk.signatus.demo.Signatus2ClientTest.testCreateEnvelopeSuccess(Signatus2ClientTest.java:167)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

sk.signatus.demo.Signatus2ClientTest.testSuccessfulAuthentication -- Time elapsed: 0.222 s <<< FAILURE!
org.opentest4j.AssertionFailedError: expected: <test_token_123> but was: <eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJJTGNWSnp4SFkyd1RDcGlLZU5Oa2VmbWZHa1ljUnBtQ1lMMUhsNG13Zm5VIn0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.qApi0VEFBNWvikcXL87li-Jc1EWnDzSkQSZ1fSYkQ_8O_hnOjmPrBcxLS81S5g2oSCpJZ665b0oN3IlLolX6XkZBnU6owxTvgWuAQkDXeMbszK7YrSgPO6mpG9Q0ORz4kFSp88D-WCWVdwXisiOxuC3Kl4-IJ59iGVrTc-Bxr4aIxn-loy6aJU049PbJJWrRQ3bdehW3bJH9cQO3U1WxC8AJSRP5vbwA7y-ytdZjfpEUyBTP7h42o46cK7XjqNVockCeU08fwrLyLLAnyy5iR9oel0h1E62qTKKURF4yVZHNPRcaAGgjLk96KeMq5N1BFi_920nvgA2nruDFAnLsAw>
	at org.junit.jupiter.api.AssertionFailureBuilder.build(AssertionFailureBuilder.java:151)
	at org.junit.jupiter.api.AssertionFailureBuilder.buildAndThrow(AssertionFailureBuilder.java:132)
	at org.junit.jupiter.api.AssertEquals.failNotEqual(AssertEquals.java:197)
	at org.junit.jupiter.api.AssertEquals.assertEquals(AssertEquals.java:182)
	at org.junit.jupiter.api.AssertEquals.assertEquals(AssertEquals.java:177)
	at org.junit.jupiter.api.Assertions.assertEquals(Assertions.java:1145)
	at sk.signatus.demo.Signatus2ClientTest.testSuccessfulAuthentication(Signatus2ClientTest.java:71)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

