<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="sk.signatus.demo.Signatus2ClientTest" time="2.979" tests="6" errors="3" skipped="0" failures="3">
  <properties>
    <property name="java.specification.version" value="21"/>
    <property name="sun.jnu.encoding" value="UTF-8"/>
    <property name="java.class.path" value="/home/<USER>/code/signatus_api/Java_demo/target/test-classes:/home/<USER>/code/signatus_api/Java_demo/target/classes:/home/<USER>/.m2/repository/com/squareup/okhttp3/okhttp/4.12.0/okhttp-4.12.0.jar:/home/<USER>/.m2/repository/com/squareup/okio/okio/3.6.0/okio-3.6.0.jar:/home/<USER>/.m2/repository/com/squareup/okio/okio-jvm/3.6.0/okio-jvm-3.6.0.jar:/home/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-stdlib-common/1.9.10/kotlin-stdlib-common-1.9.10.jar:/home/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-stdlib-jdk8/1.8.21/kotlin-stdlib-jdk8-1.8.21.jar:/home/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-stdlib/1.8.21/kotlin-stdlib-1.8.21.jar:/home/<USER>/.m2/repository/org/jetbrains/annotations/13.0/annotations-13.0.jar:/home/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-stdlib-jdk7/1.8.21/kotlin-stdlib-jdk7-1.8.21.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.16.1/jackson-core-2.16.1.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.16.1/jackson-databind-2.16.1.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.16.1/jackson-annotations-2.16.1.jar:/home/<USER>/.m2/repository/org/slf4j/slf4j-api/2.0.9/slf4j-api-2.0.9.jar:/home/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.4.14/logback-classic-1.4.14.jar:/home/<USER>/.m2/repository/ch/qos/logback/logback-core/1.4.14/logback-core-1.4.14.jar:/home/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter/5.10.1/junit-jupiter-5.10.1.jar:/home/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.10.1/junit-jupiter-api-5.10.1.jar:/home/<USER>/.m2/repository/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.jar:/home/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.10.1/junit-platform-commons-1.10.1.jar:/home/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/home/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-params/5.10.1/junit-jupiter-params-5.10.1.jar:/home/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.10.1/junit-jupiter-engine-5.10.1.jar:/home/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.10.1/junit-platform-engine-1.10.1.jar:/home/<USER>/.m2/repository/org/mockito/mockito-core/5.8.0/mockito-core-5.8.0.jar:/home/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.14.10/byte-buddy-1.14.10.jar:/home/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.14.10/byte-buddy-agent-1.14.10.jar:/home/<USER>/.m2/repository/org/objenesis/objenesis/3.3/objenesis-3.3.jar:/home/<USER>/.m2/repository/org/mockito/mockito-junit-jupiter/5.8.0/mockito-junit-jupiter-5.8.0.jar:/home/<USER>/.m2/repository/com/squareup/okhttp3/mockwebserver/4.12.0/mockwebserver-4.12.0.jar:/home/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/home/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar:"/>
    <property name="java.vm.vendor" value="Ubuntu"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="java.vendor.url" value="https://ubuntu.com/"/>
    <property name="user.timezone" value="Europe/Bratislava"/>
    <property name="os.name" value="Linux"/>
    <property name="java.vm.specification.version" value="21"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="sun.boot.library.path" value="/usr/lib/jvm/java-21-openjdk-amd64/lib"/>
    <property name="sun.java.command" value="/home/<USER>/code/signatus_api/Java_demo/target/surefire/surefirebooter-20250609101343918_3.jar /home/<USER>/code/signatus_api/Java_demo/target/surefire 2025-06-09T10-13-43_708-jvmRun1 surefire-20250609101343918_1tmp surefire_0-20250609101343918_2tmp"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="/home/<USER>/code/signatus_api/Java_demo/target/test-classes:/home/<USER>/code/signatus_api/Java_demo/target/classes:/home/<USER>/.m2/repository/com/squareup/okhttp3/okhttp/4.12.0/okhttp-4.12.0.jar:/home/<USER>/.m2/repository/com/squareup/okio/okio/3.6.0/okio-3.6.0.jar:/home/<USER>/.m2/repository/com/squareup/okio/okio-jvm/3.6.0/okio-jvm-3.6.0.jar:/home/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-stdlib-common/1.9.10/kotlin-stdlib-common-1.9.10.jar:/home/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-stdlib-jdk8/1.8.21/kotlin-stdlib-jdk8-1.8.21.jar:/home/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-stdlib/1.8.21/kotlin-stdlib-1.8.21.jar:/home/<USER>/.m2/repository/org/jetbrains/annotations/13.0/annotations-13.0.jar:/home/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-stdlib-jdk7/1.8.21/kotlin-stdlib-jdk7-1.8.21.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.16.1/jackson-core-2.16.1.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.16.1/jackson-databind-2.16.1.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.16.1/jackson-annotations-2.16.1.jar:/home/<USER>/.m2/repository/org/slf4j/slf4j-api/2.0.9/slf4j-api-2.0.9.jar:/home/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.4.14/logback-classic-1.4.14.jar:/home/<USER>/.m2/repository/ch/qos/logback/logback-core/1.4.14/logback-core-1.4.14.jar:/home/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter/5.10.1/junit-jupiter-5.10.1.jar:/home/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.10.1/junit-jupiter-api-5.10.1.jar:/home/<USER>/.m2/repository/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.jar:/home/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.10.1/junit-platform-commons-1.10.1.jar:/home/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/home/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-params/5.10.1/junit-jupiter-params-5.10.1.jar:/home/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.10.1/junit-jupiter-engine-5.10.1.jar:/home/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.10.1/junit-platform-engine-1.10.1.jar:/home/<USER>/.m2/repository/org/mockito/mockito-core/5.8.0/mockito-core-5.8.0.jar:/home/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.14.10/byte-buddy-1.14.10.jar:/home/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.14.10/byte-buddy-agent-1.14.10.jar:/home/<USER>/.m2/repository/org/objenesis/objenesis/3.3/objenesis-3.3.jar:/home/<USER>/.m2/repository/org/mockito/mockito-junit-jupiter/5.8.0/mockito-junit-jupiter-5.8.0.jar:/home/<USER>/.m2/repository/com/squareup/okhttp3/mockwebserver/4.12.0/mockwebserver-4.12.0.jar:/home/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/home/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar:"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="/home/<USER>"/>
    <property name="user.language" value="en"/>
    <property name="java.specification.vendor" value="Oracle Corporation"/>
    <property name="java.version.date" value="2025-04-15"/>
    <property name="java.home" value="/usr/lib/jvm/java-21-openjdk-amd64"/>
    <property name="file.separator" value="/"/>
    <property name="basedir" value="/home/<USER>/code/signatus_api/Java_demo"/>
    <property name="java.vm.compressedOopsMode" value="32-bit"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="surefire.real.class.path" value="/home/<USER>/code/signatus_api/Java_demo/target/surefire/surefirebooter-20250609101343918_3.jar"/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="21.0.7+6-Ubuntu-0ubuntu124.04"/>
    <property name="user.name" value="tomas"/>
    <property name="stdout.encoding" value="UTF-8"/>
    <property name="path.separator" value=":"/>
    <property name="os.version" value="5.15.146.1-microsoft-standard-WSL2"/>
    <property name="java.runtime.name" value="OpenJDK Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="OpenJDK 64-Bit Server VM"/>
    <property name="localRepository" value="/home/<USER>/.m2/repository"/>
    <property name="java.vendor.url.bug" value="https://bugs.launchpad.net/ubuntu/+source/openjdk-21"/>
    <property name="java.io.tmpdir" value="/tmp"/>
    <property name="java.version" value="21.0.7"/>
    <property name="user.dir" value="/home/<USER>/code/signatus_api/Java_demo"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="native.encoding" value="UTF-8"/>
    <property name="java.library.path" value="/usr/java/packages/lib:/usr/lib/x86_64-linux-gnu/jni:/lib/x86_64-linux-gnu:/usr/lib/x86_64-linux-gnu:/usr/lib/jni:/lib:/usr/lib"/>
    <property name="stderr.encoding" value="UTF-8"/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="java.vendor" value="Ubuntu"/>
    <property name="java.vm.version" value="21.0.7+6-Ubuntu-0ubuntu124.04"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="65.0"/>
  </properties>
  <testcase name="testGetEnvelopeSuccess" classname="sk.signatus.demo.Signatus2ClientTest" time="1.596">
    <error message="API chyba: HTTP 500" type="Signatus2Exception"><![CDATA[Signatus2Exception: API chyba: HTTP 500 (HTTP 500) - Response: {"message":"Invalid UUID string: envelope_123","value":""}
	at sk.signatus.demo.Signatus2Client.handleResponse(Signatus2Client.java:180)
	at sk.signatus.demo.Signatus2Client.getEnvelope(Signatus2Client.java:210)
	at sk.signatus.demo.Signatus2ClientTest.testGetEnvelopeSuccess(Signatus2ClientTest.java:113)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
]]></error>
    <system-out><![CDATA[10:13:44,801 |-INFO in ch.qos.logback.classic.LoggerContext[default] - This is logback-classic version 1.4.14
10:13:44,802 |-INFO in ch.qos.logback.classic.util.ContextInitializer@5c44c582 - No custom configurators were discovered as a service.
10:13:44,802 |-INFO in ch.qos.logback.classic.util.ContextInitializer@5c44c582 - Trying to configure with ch.qos.logback.classic.joran.SerializedModelConfigurator
10:13:44,803 |-INFO in ch.qos.logback.classic.util.ContextInitializer@5c44c582 - Constructed configurator of type class ch.qos.logback.classic.joran.SerializedModelConfigurator
10:13:44,809 |-INFO in ch.qos.logback.classic.LoggerContext[default] - Could NOT find resource [logback-test.scmo]
10:13:44,809 |-INFO in ch.qos.logback.classic.LoggerContext[default] - Could NOT find resource [logback.scmo]
10:13:44,815 |-INFO in ch.qos.logback.classic.util.ContextInitializer@5c44c582 - ch.qos.logback.classic.joran.SerializedModelConfigurator.configure() call lasted 7 milliseconds. ExecutionStatus=INVOKE_NEXT_IF_ANY
10:13:44,815 |-INFO in ch.qos.logback.classic.util.ContextInitializer@5c44c582 - Trying to configure with ch.qos.logback.classic.util.DefaultJoranConfigurator
10:13:44,816 |-INFO in ch.qos.logback.classic.util.ContextInitializer@5c44c582 - Constructed configurator of type class ch.qos.logback.classic.util.DefaultJoranConfigurator
10:13:44,816 |-INFO in ch.qos.logback.classic.LoggerContext[default] - Could NOT find resource [logback-test.xml]
10:13:44,821 |-INFO in ch.qos.logback.classic.LoggerContext[default] - Found resource [logback.xml] at [file:/home/<USER>/code/signatus_api/Java_demo/target/classes/logback.xml]
10:13:44,920 |-INFO in ch.qos.logback.core.model.processor.AppenderModelHandler - Processing appender named [CONSOLE]
10:13:44,920 |-INFO in ch.qos.logback.core.model.processor.AppenderModelHandler - About to instantiate appender of type [ch.qos.logback.core.ConsoleAppender]
10:13:44,926 |-INFO in ch.qos.logback.core.model.processor.ImplicitModelHandler - Assuming default type [ch.qos.logback.classic.encoder.PatternLayoutEncoder] for [encoder] property
10:13:44,964 |-INFO in ch.qos.logback.core.model.processor.AppenderModelHandler - Processing appender named [FILE]
10:13:44,964 |-INFO in ch.qos.logback.core.model.processor.AppenderModelHandler - About to instantiate appender of type [ch.qos.logback.core.rolling.RollingFileAppender]
10:13:44,985 |-INFO in c.q.l.core.rolling.TimeBasedRollingPolicy@1741786839 - setting totalSizeCap to 300 MB
10:13:44,988 |-INFO in c.q.l.core.rolling.TimeBasedRollingPolicy@1741786839 - No compression will be used
10:13:44,989 |-INFO in c.q.l.core.rolling.TimeBasedRollingPolicy@1741786839 - Will use the pattern logs/signatus2-demo.%d{yyyy-MM-dd}.%i.log for the active file
10:13:45,023 |-INFO in ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP@2c78d320 - The date pattern is 'yyyy-MM-dd' from file name pattern 'logs/signatus2-demo.%d{yyyy-MM-dd}.%i.log'.
10:13:45,023 |-INFO in ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP@2c78d320 - Roll-over at midnight.
10:13:45,030 |-INFO in ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP@2c78d320 - Setting initial period to 2025-06-09T08:01:39.893Z
10:13:45,030 |-WARN in ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP@2c78d320 - SizeAndTimeBasedFNATP is deprecated. Use SizeAndTimeBasedRollingPolicy instead
10:13:45,030 |-WARN in ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP@2c78d320 - For more information see http://logback.qos.ch/manual/appenders.html#SizeAndTimeBasedRollingPolicy
10:13:45,033 |-INFO in ch.qos.logback.core.model.processor.ImplicitModelHandler - Assuming default type [ch.qos.logback.classic.encoder.PatternLayoutEncoder] for [encoder] property
10:13:45,037 |-INFO in ch.qos.logback.core.rolling.RollingFileAppender[FILE] - Active log file name: logs/signatus2-demo.log
10:13:45,037 |-INFO in ch.qos.logback.core.rolling.RollingFileAppender[FILE] - File property is set to [logs/signatus2-demo.log]
10:13:45,039 |-INFO in ch.qos.logback.classic.model.processor.LoggerModelHandler - Setting level of logger [sk.signatus.demo] to INFO
10:13:45,041 |-INFO in ch.qos.logback.classic.model.processor.LoggerModelHandler - Setting additivity of logger [sk.signatus.demo] to false
10:13:45,041 |-INFO in ch.qos.logback.core.model.processor.AppenderRefModelHandler - Attaching appender named [CONSOLE] to Logger[sk.signatus.demo]
10:13:45,042 |-INFO in ch.qos.logback.core.model.processor.AppenderRefModelHandler - Attaching appender named [FILE] to Logger[sk.signatus.demo]
10:13:45,042 |-INFO in ch.qos.logback.classic.model.processor.LoggerModelHandler - Setting level of logger [okhttp3] to INFO
10:13:45,042 |-INFO in ch.qos.logback.classic.model.processor.LoggerModelHandler - Setting additivity of logger [okhttp3] to false
10:13:45,042 |-INFO in ch.qos.logback.core.model.processor.AppenderRefModelHandler - Attaching appender named [CONSOLE] to Logger[okhttp3]
10:13:45,042 |-INFO in ch.qos.logback.core.model.processor.AppenderRefModelHandler - Attaching appender named [FILE] to Logger[okhttp3]
10:13:45,042 |-INFO in ch.qos.logback.classic.model.processor.RootLoggerModelHandler - Setting level of ROOT logger to WARN
10:13:45,042 |-INFO in ch.qos.logback.core.model.processor.AppenderRefModelHandler - Attaching appender named [CONSOLE] to Logger[ROOT]
10:13:45,042 |-INFO in ch.qos.logback.core.model.processor.AppenderRefModelHandler - Attaching appender named [FILE] to Logger[ROOT]
10:13:45,042 |-INFO in ch.qos.logback.core.model.processor.DefaultProcessor@132e0cc - End of configuration.
10:13:45,043 |-INFO in ch.qos.logback.classic.joran.JoranConfigurator@7b205dbd - Registering current configuration as safe fallback point
10:13:45,043 |-INFO in ch.qos.logback.classic.util.ContextInitializer@5c44c582 - ch.qos.logback.classic.util.DefaultJoranConfigurator.configure() call lasted 227 milliseconds. ExecutionStatus=DO_NOT_INVOKE_NEXT_IF_ANY

2025-06-09 10:13:45.532 [main] INFO  sk.signatus.demo.Signatus2Client - Získavam informácie o obálke envelope_123
2025-06-09 10:13:45.535 [main] INFO  sk.signatus.demo.Signatus2Client - Začínam OAuth autentifikáciu...
2025-06-09 10:13:46.050 [main] INFO  sk.signatus.demo.Signatus2Client - OAuth autentifikácia úspešná
]]></system-out>
  </testcase>
  <testcase name="testGetEnvelopeNotFound" classname="sk.signatus.demo.Signatus2ClientTest" time="0.329">
    <failure message="Unexpected exception type thrown, expected: &lt;sk.signatus.demo.exception.NotFoundError&gt; but was: &lt;sk.signatus.demo.exception.Signatus2Exception&gt;" type="org.opentest4j.AssertionFailedError"><![CDATA[org.opentest4j.AssertionFailedError: Unexpected exception type thrown, expected: <sk.signatus.demo.exception.NotFoundError> but was: <sk.signatus.demo.exception.Signatus2Exception>
	at org.junit.jupiter.api.AssertionFailureBuilder.build(AssertionFailureBuilder.java:151)
	at org.junit.jupiter.api.AssertThrows.assertThrows(AssertThrows.java:67)
	at org.junit.jupiter.api.AssertThrows.assertThrows(AssertThrows.java:35)
	at org.junit.jupiter.api.Assertions.assertThrows(Assertions.java:3115)
	at sk.signatus.demo.Signatus2ClientTest.testGetEnvelopeNotFound(Signatus2ClientTest.java:140)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
Caused by: Signatus2Exception: API chyba: HTTP 500 (HTTP 500) - Response: {"message":"Invalid UUID string: nonexistent_id","value":""}
	at sk.signatus.demo.Signatus2Client.handleResponse(Signatus2Client.java:180)
	at sk.signatus.demo.Signatus2Client.getEnvelope(Signatus2Client.java:210)
	at sk.signatus.demo.Signatus2ClientTest.lambda$testGetEnvelopeNotFound$1(Signatus2ClientTest.java:140)
	at org.junit.jupiter.api.AssertThrows.assertThrows(AssertThrows.java:53)
	... 6 more
]]></failure>
    <system-out><![CDATA[2025-06-09 10:13:46.230 [main] INFO  sk.signatus.demo.Signatus2Client - Získavam informácie o obálke nonexistent_id
2025-06-09 10:13:46.231 [main] INFO  sk.signatus.demo.Signatus2Client - Začínam OAuth autentifikáciu...
2025-06-09 10:13:46.451 [main] INFO  sk.signatus.demo.Signatus2Client - OAuth autentifikácia úspešná
]]></system-out>
  </testcase>
  <testcase name="testAuthenticationFailure" classname="sk.signatus.demo.Signatus2ClientTest" time="0.227">
    <failure message="Expected sk.signatus.demo.exception.AuthenticationException to be thrown, but nothing was thrown." type="org.opentest4j.AssertionFailedError"><![CDATA[org.opentest4j.AssertionFailedError: Expected sk.signatus.demo.exception.AuthenticationException to be thrown, but nothing was thrown.
	at org.junit.jupiter.api.AssertionFailureBuilder.build(AssertionFailureBuilder.java:152)
	at org.junit.jupiter.api.AssertThrows.assertThrows(AssertThrows.java:73)
	at org.junit.jupiter.api.AssertThrows.assertThrows(AssertThrows.java:35)
	at org.junit.jupiter.api.Assertions.assertThrows(Assertions.java:3115)
	at sk.signatus.demo.Signatus2ClientTest.testAuthenticationFailure(Signatus2ClientTest.java:90)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
]]></failure>
    <system-out><![CDATA[2025-06-09 10:13:46.558 [main] INFO  sk.signatus.demo.Signatus2Client - Začínam OAuth autentifikáciu...
2025-06-09 10:13:46.780 [main] INFO  sk.signatus.demo.Signatus2Client - OAuth autentifikácia úspešná
]]></system-out>
  </testcase>
  <testcase name="testGetProcessInfoSuccess" classname="sk.signatus.demo.Signatus2ClientTest" time="0.269">
    <error message="API chyba: HTTP 500" type="Signatus2Exception"><![CDATA[Signatus2Exception: API chyba: HTTP 500 (HTTP 500) - Response: {"message":"err.process.load","value":""}
	at sk.signatus.demo.Signatus2Client.handleResponse(Signatus2Client.java:180)
	at sk.signatus.demo.Signatus2Client.getProcessInfo(Signatus2Client.java:323)
	at sk.signatus.demo.Signatus2ClientTest.testGetProcessInfoSuccess(Signatus2ClientTest.java:215)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
]]></error>
    <system-out><![CDATA[2025-06-09 10:13:46.787 [main] INFO  sk.signatus.demo.Signatus2Client - Získavam informácie o procese process_123
2025-06-09 10:13:46.788 [main] INFO  sk.signatus.demo.Signatus2Client - Začínam OAuth autentifikáciu...
2025-06-09 10:13:47.015 [main] INFO  sk.signatus.demo.Signatus2Client - OAuth autentifikácia úspešná
]]></system-out>
  </testcase>
  <testcase name="testCreateEnvelopeSuccess" classname="sk.signatus.demo.Signatus2ClientTest" time="0.299">
    <error message="Neplatná autentifikácia - skontrolujte prihlasovacie údaje" type="AuthenticationException"><![CDATA[AuthenticationException: Neplatná autentifikácia - skontrolujte prihlasovacie údaje (HTTP 401)
	at sk.signatus.demo.Signatus2Client.handleResponse(Signatus2Client.java:155)
	at sk.signatus.demo.Signatus2Client.createEnvelope(Signatus2Client.java:414)
	at sk.signatus.demo.Signatus2ClientTest.testCreateEnvelopeSuccess(Signatus2ClientTest.java:167)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
]]></error>
    <system-out><![CDATA[2025-06-09 10:13:47.056 [main] INFO  sk.signatus.demo.Signatus2Client - Vytváram novú obálku: New envelope
2025-06-09 10:13:47.074 [main] INFO  sk.signatus.demo.Signatus2Client - Začínam OAuth autentifikáciu...
2025-06-09 10:13:47.290 [main] INFO  sk.signatus.demo.Signatus2Client - OAuth autentifikácia úspešná
]]></system-out>
  </testcase>
  <testcase name="testSuccessfulAuthentication" classname="sk.signatus.demo.Signatus2ClientTest" time="0.222">
    <failure message="expected: &lt;test_token_123&gt; but was: &lt;eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJJTGNWSnp4SFkyd1RDcGlLZU5Oa2VmbWZHa1ljUnBtQ1lMMUhsNG13Zm5VIn0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.qApi0VEFBNWvikcXL87li-Jc1EWnDzSkQSZ1fSYkQ_8O_hnOjmPrBcxLS81S5g2oSCpJZ665b0oN3IlLolX6XkZBnU6owxTvgWuAQkDXeMbszK7YrSgPO6mpG9Q0ORz4kFSp88D-WCWVdwXisiOxuC3Kl4-IJ59iGVrTc-Bxr4aIxn-loy6aJU049PbJJWrRQ3bdehW3bJH9cQO3U1WxC8AJSRP5vbwA7y-ytdZjfpEUyBTP7h42o46cK7XjqNVockCeU08fwrLyLLAnyy5iR9oel0h1E62qTKKURF4yVZHNPRcaAGgjLk96KeMq5N1BFi_920nvgA2nruDFAnLsAw&gt;" type="org.opentest4j.AssertionFailedError"><![CDATA[org.opentest4j.AssertionFailedError: expected: <test_token_123> but was: <eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJJTGNWSnp4SFkyd1RDcGlLZU5Oa2VmbWZHa1ljUnBtQ1lMMUhsNG13Zm5VIn0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.qApi0VEFBNWvikcXL87li-Jc1EWnDzSkQSZ1fSYkQ_8O_hnOjmPrBcxLS81S5g2oSCpJZ665b0oN3IlLolX6XkZBnU6owxTvgWuAQkDXeMbszK7YrSgPO6mpG9Q0ORz4kFSp88D-WCWVdwXisiOxuC3Kl4-IJ59iGVrTc-Bxr4aIxn-loy6aJU049PbJJWrRQ3bdehW3bJH9cQO3U1WxC8AJSRP5vbwA7y-ytdZjfpEUyBTP7h42o46cK7XjqNVockCeU08fwrLyLLAnyy5iR9oel0h1E62qTKKURF4yVZHNPRcaAGgjLk96KeMq5N1BFi_920nvgA2nruDFAnLsAw>
	at org.junit.jupiter.api.AssertionFailureBuilder.build(AssertionFailureBuilder.java:151)
	at org.junit.jupiter.api.AssertionFailureBuilder.buildAndThrow(AssertionFailureBuilder.java:132)
	at org.junit.jupiter.api.AssertEquals.failNotEqual(AssertEquals.java:197)
	at org.junit.jupiter.api.AssertEquals.assertEquals(AssertEquals.java:182)
	at org.junit.jupiter.api.AssertEquals.assertEquals(AssertEquals.java:177)
	at org.junit.jupiter.api.Assertions.assertEquals(Assertions.java:1145)
	at sk.signatus.demo.Signatus2ClientTest.testSuccessfulAuthentication(Signatus2ClientTest.java:71)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
]]></failure>
    <system-out><![CDATA[2025-06-09 10:13:47.356 [main] INFO  sk.signatus.demo.Signatus2Client - Začínam OAuth autentifikáciu...
2025-06-09 10:13:47.572 [main] INFO  sk.signatus.demo.Signatus2Client - OAuth autentifikácia úspešná
]]></system-out>
  </testcase>
</testsuite>