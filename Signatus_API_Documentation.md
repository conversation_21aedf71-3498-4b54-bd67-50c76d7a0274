# Signatus API Dokumentácia (v2.0 - Aktualizované)

Komplexná dokumentácia REST API služieb pre systémy Signatus2 a STS (Signature Transaction Service). Dokumentácia je aktualizovaná podľa najnovšej verzie Postman kolekcie.

## 🆕 Zmeny vo verzii 2.0

- **Nové API cesty** - Všetky endpointy používajú `/signatus/api/` prefix
- **Nová štruktúra obálok** - Komplexnejšie procesy a dokumenty
- **Nové endpointy** - Procesy, kompletné informácie, dáta obálky, vymazanie
- **Form-data podpora** - Priame nahrávanie súborov pri vytváraní obálky
- **Vylepšené procesy** - Podpora pre rôzne typy podpisov a metódy dokumentov

## Obsah

1. [Úvod](#úvod)
2. [Signatus2 API (Aktualizované)](#signatus2-api)
3. [STS API](#sts-api)
4. [Všeobecné informácie](#všeobecné-informácie)
5. [Príklady implementácie](#príklady-implementácie)
6. [Migračný sprievodca](#migračný-sprievodca)

## Úvod

Signatus poskytuje dva hlavné REST API systémy pre elektronické podpisovanie dokumentov:

- **Signatus2 API**: Pokročilý systém pre správu obálok, dokumentov a podpisov s OAuth autentifikáciou
- **STS API**: Jednoduchší systém pre správu dokumentov a podpisov s autentifikáciou pomocou API kľúča

## Signatus2 API (Aktualizované)

### Base URL
```
https://ds-sts.ana.sk/signatus/api
```

**⚠️ Dôležitá zmena:** Všetky API endpointy teraz používajú prefix `/signatus/api/` namiesto `/workflow/api/`.

### Autentifikácia

Signatus2 API používa OAuth 2.0 autentifikáciu s Bearer tokenmi.

#### Získanie autentifikačného tokenu

**Endpoint:** `POST /realms/Signatus/protocol/openid-connect/token`

**URL:** `https://dsoauth.ana.sk/realms/Signatus/protocol/openid-connect/token`

**Headers:**
```
Content-Type: application/x-www-form-urlencoded
Authorization: Basic c2lnbmF0dXM6ZmI1VlhSbk1aMkliQUlsaWs3dUJTMW1JWkUyUEpkQk4=
```

**Request Body (form-urlencoded):**
```
grant_type=password
username=iauser
password=S#DgfsH64
scope=openid
```

**Response:**
```json
{
  "access_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "id_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "Bearer",
  "expires_in": 3600
}
```

**HTTP Status kódy:**
- `200 OK` - Úspešná autentifikácia
- `401 Unauthorized` - Neplatné prihlasovacie údaje
- `400 Bad Request` - Chybné parametre požiadavky

### Obálky (Envelopes) - Aktualizované

#### Získanie základných informácií o obálke

**Endpoint:** `GET /signatus/api/envelope/{envelopeId}`

**Headers:**
```
Authorization: Bearer {access_token}
Content-Type: application/x-www-form-urlencoded
```

**Path Parameters:**
- `envelopeId` (string, required) - Jedinečný identifikátor obálky

**Response:**
```json
{
  "id": "envelope_123",
  "state": "ready",
  "expirationtime": 1234567890000
}
```

**HTTP Status kódy:**
- `200 OK` - Úspešné získanie informácií
- `401 Unauthorized` - Neplatný alebo chýbajúci token
- `404 Not Found` - Obálka nebola nájdená
- `409 Conflict` - Konflikt pri prístupe k obálke

#### Získanie kompletných informácií o obálke 🆕

**Endpoint:** `GET /signatus/api/envelope/complete/{envelopeId}`

**Headers:**
```
Authorization: Bearer {access_token}
Content-Type: application/x-www-form-urlencoded
```

**Path Parameters:**
- `envelopeId` (string, required) - Jedinečný identifikátor obálky

**Response:**
```json
{
  "id": "envelope_123",
  "state": "ready",
  "envelopetype": "default",
  "expirationtime": 1234567890000,
  "processes": [
    {
      "id": "process_456",
      "state": "ready",
      "signer": "user1",
      "signing": "document",
      "sigtype": "bio",
      "documentsProcess": [
        {
          "documentid": "doc_789",
          "reference": "document.pdf",
          "method": "write"
        }
      ]
    }
  ]
}
```

#### Získanie dát obálky 🆕

**Endpoint:** `GET /signatus/api/envelopeData/{envelopeId}`

**Headers:**
```
Authorization: Bearer {access_token}
Content-Type: application/x-www-form-urlencoded
```

**Path Parameters:**
- `envelopeId` (string, required) - Jedinečný identifikátor obálky

**Response:**
Raw textové dáta obálky (nie JSON)

#### Vytvorenie novej obálky 🔄 (Aktualizované)

**Endpoint:** `POST /signatus/api/envelope`

**Headers:**
```
Authorization: Bearer {access_token}
CSRF-SIGNATUS-TOKEN: 0bed913d-90bc-48be-ba95-55c8e63da864
```

**Request Body (form-data):**
```
dataText: {
  "envelopetype": "default",
  "state": "ready",
  "type": "menu1",
  "expirationtime": 1234567890000,
  "rules": {
    "resulturl": "https://www.signatus.com",
    "lang": "sk",
    "welcomeScreen": "none",
    "emailFinal": "<EMAIL>; <EMAIL>",
    "emailTo": "<EMAIL>",
    "emailFrom": "<EMAIL>",
    "nameFrom": "Sender Name"
  },
  "processes": [
    {
      "signer": "user1",
      "signing": "document",
      "sigtype": "bio",
      "documents": [
        {
          "method": "write",
          "reference": "document.pdf"
        }
      ]
    }
  ],
  "extension": {}
}

document.pdf: [binary file data]
```

**Request Body Parameters:**
- `dataText` (string, required) - JSON štruktúra s konfiguráciou obálky
- `{filename}` (file, optional) - Súbory dokumentov odkazované v processes

**dataText štruktúra:**
- `envelopetype` (string) - Typ obálky ("default")
- `state` (string) - Stav obálky ("ready")
- `expirationtime` (number) - Čas expirácie v milisekundách
- `rules` (object) - Pravidlá a nastavenia
- `processes` (array) - Zoznam procesov s dokumentmi
- `extension` (object) - Rozšírenia (prázdne)

**Response:**
```json
{
  "id": "envelope_789",
  "state": "ready",
  "expirationtime": 1234567890000,
  "processes": [
    {
      "id": "process_123",
      "state": "ready"
    }
  ]
}
```

**HTTP Status kódy:**
- `200 OK` - Obálka bola úspešne vytvorená
- `400 Bad Request` - Chybné parametre požiadavky
- `401 Unauthorized` - Neplatný alebo chýbajúci token

#### Vymazanie obálky 🆕

**Endpoint:** `DELETE /signatus/api/envelope/{envelopeId}`

**Headers:**
```
Authorization: Bearer {access_token}
Content-Type: application/x-www-form-urlencoded
```

**Path Parameters:**
- `envelopeId` (string, required) - Jedinečný identifikátor obálky

**Response:**
```json
{
  "message": "msg.envelope.delete.success"
}
```

**HTTP Status kódy:**
- `200 OK` - Obálka bola úspešne vymazaná
- `401 Unauthorized` - Neplatný alebo chýbajúci token
- `404 Not Found` - Obálka nebola nájdená
- `500 Internal Server Error` - Chyba pri mazaní

### Procesy 🆕

#### Získanie informácií o procese

**Endpoint:** `GET /signatus/api/process/{processId}`

**Headers:**
```
Authorization: Bearer {access_token}
Content-Type: application/json
```

**Path Parameters:**
- `processId` (string, required) - Jedinečný identifikátor procesu

**Response:**
```json
{
  "id": "process_123",
  "envelopeid": "envelope_456",
  "state": "ready",
  "signer": "user1",
  "signing": "document",
  "sigtype": "bio",
  "documentsProcess": [
    {
      "documentid": "doc_789",
      "reference": "document.pdf",
      "method": "write"
    }
  ],
  "envelopetypeid": "1",
  "tenant": {
    "id": "tenant_001",
    "name": "Demo Tenant"
  }
}
```

**Response Parameters:**
- `id` (string) - ID procesu
- `envelopeid` (string) - ID obálky
- `state` (string) - Stav procesu ("ready", "completed", "failed")
- `signer` (string) - Identifikátor podpisovateľa
- `signing` (string) - Typ podpisovania ("document")
- `sigtype` (string) - Typ podpisu ("bio", "certificate", "simple")
- `documentsProcess` (array) - Dokumenty v procese
- `envelopetypeid` (string) - ID typu obálky
- `tenant` (object) - Informácie o tenantovi

**documentsProcess štruktúra:**
- `documentid` (string) - ID dokumentu
- `reference` (string) - Referencia na súbor
- `method` (string) - Metóda spracovania ("write", "read", "optional")

**HTTP Status kódy:**
- `200 OK` - Úspešné získanie informácií
- `401 Unauthorized` - Neplatný alebo chýbajúci token
- `404 Not Found` - Proces nebol nájdený

## STS API

### Base URL
```
https://ds-sts.ana.sk
```

### Autentifikácia

STS API používa autentifikáciu pomocou API kľúča posielaného v hlavičke `x-api-key`.

**API kľúč pre testovacie účely:**
```
961657375627_7d19b177854d7c95c2a0147d42585b613bfe71cde3263bf19d37cb16131b9e43695d74907a3c346b1f708e6c1fa2df1d4fb2eb81b1df145964187c64862f81df
```

### Dokumenty

#### Vytvorenie nového dokumentu

**Endpoint:** `POST /api/document`

**Headers:**
```
x-api-key: {api_key}
Content-Type: application/json
CSRF-SIGNATUS-TOKEN: 0bed913d-90bc-48be-ba95-55c8e63da864 (voliteľné)
```

**Request Body:**
```json
{
  "resulturl": "https://www.signatus.com",
  "state": "tosign",
  "expirationTime": "1902288000000",
  "rules": "emailNotification_emailFinal-email@example.com_emailTo-email@example.com_emailFrom-sender@example.com_nameFrom-Sender Name_language-sk",
  "filedata": {
    "content": "JVBERi0xLjcNJeLjz9MNCjIwNCAwIG9iag08PC9MaW5lYXJpemVkIDEvTCA0NjcyMDEvTyAyMDkvRSA0NTE3NzEvTiAxL1QgNDY2ODIwL0ggWyA2NTcgMjgzXT4+DWVuZG9iag0..."
  }
}
```

**Request Body Parameters:**
- `resulturl` (string, required) - URL pre presmerovanie po dokončení procesu
- `state` (string, required) - Stav dokumentu (`tosign`, `signed`, `draft`)
- `expirationTime` (string, required) - Čas expirácie v milisekundách (Unix timestamp)
- `rules` (string, required) - Pravidlá pre notifikácie a nastavenia
- `filedata.content` (string, required) - Obsah súboru kódovaný v Base64

**Response:**
```json
{
  "documentId": "doc_123456",
  "result": "signature_request_789",
  "status": "created",
  "message": "Document created successfully"
}
```

**HTTP Status kódy:**
- `201 Created` - Dokument bol úspešne vytvorený
- `400 Bad Request` - Chybné parametre požiadavky
- `401 Unauthorized` - Neplatný API kľúč
- `413 Payload Too Large` - Súbor je príliš veľký

#### Získanie informácií o dokumente

**Endpoint:** `GET /api/document/{documentId}`

**Headers:**
```
x-api-key: {api_key}
```

**Path Parameters:**
- `documentId` (string, required) - Jedinečný identifikátor dokumentu

**Response:**
```json
{
  "documentId": "doc_123456",
  "state": "tosign",
  "created": "2024-01-15T10:30:00Z",
  "expirationTime": "1902288000000",
  "resulturl": "https://www.signatus.com",
  "signatures": [
    {
      "id": "sig_789",
      "status": "pending",
      "signerEmail": "<EMAIL>"
    }
  ]
}
```

**HTTP Status kódy:**
- `200 OK` - Úspešné získanie informácií
- `401 Unauthorized` - Neplatný API kľúč
- `404 Not Found` - Dokument nebol nájdený

### Podpisy

#### Vytvorenie nového podpisu

**Endpoint:** `POST /api/signature`

**Headers:**
```
x-api-key: {api_key}
Content-Type: application/json
```

**Request Body:**
```json
{
  "documentId": "doc_123456",
  "signerEmail": "<EMAIL>",
  "signerName": "John Doe",
  "position": {
    "page": 1,
    "x": 100,
    "y": 200
  }
}
```

**Response:**
```json
{
  "signatureId": "sig_789",
  "documentId": "doc_123456",
  "status": "pending",
  "signerEmail": "<EMAIL>",
  "created": "2024-01-15T11:00:00Z"
}
```

**HTTP Status kódy:**
- `201 Created` - Podpis bol úspešne vytvorený
- `400 Bad Request` - Chybné parametre požiadavky
- `401 Unauthorized` - Neplatný API kľúč
- `404 Not Found` - Dokument nebol nájdený

#### Podpísanie dokumentu

**Endpoint:** `POST /api/signature/{signatureId}/sign`

**Headers:**
```
x-api-key: {api_key}
Content-Type: application/json
```

**Path Parameters:**
- `signatureId` (string, required) - Jedinečný identifikátor podpisu

**Request Body (voliteľné):**
```json
{
  "certificate": "base64_encoded_certificate",
  "signatureData": "base64_encoded_signature"
}
```

**Response:**
```json
{
  "signatureId": "sig_789",
  "status": "signed",
  "signedAt": "2024-01-15T11:30:00Z",
  "documentId": "doc_123456"
}
```

**HTTP Status kódy:**
- `200 OK` - Dokument bol úspešne podpísaný
- `400 Bad Request` - Chybné parametre požiadavky
- `401 Unauthorized` - Neplatný API kľúč
- `404 Not Found` - Podpis nebol nájdený
- `409 Conflict` - Podpis už bol vykonaný

## Všeobecné informácie

### Formáty dát

#### Dátum a čas
Všetky dátumy a časy sú vo formáte ISO 8601 UTC:
```
2024-01-15T10:30:00Z
```

#### Base64 kódovanie
Obsah súborov sa posiela kódovaný v Base64:
```
JVBERi0xLjcNJeLjz9MNCjIwNCAwIG9iag08PC9MaW5lYXJpemVkIDEvTCA0NjcyMDEvTyAyMDkvRSA0NTE3NzEvTiAxL1QgNDY2ODIwL0ggWyA2NTcgMjgzXT4+DWVuZG9iag0...
```

### Podporované formáty súborov

- **PDF** - Preferovaný formát pre dokumenty
- **DOC/DOCX** - Microsoft Word dokumenty
- **TXT** - Textové súbory
- **JPG/PNG** - Obrázky (obmedzená podpora)

### Limity

- **Maximálna veľkosť súboru**: 10 MB
- **Maximálny počet dokumentov v obálke**: 50
- **Maximálny počet príjemcov**: 100
- **Doba platnosti tokenu**: 3600 sekúnd (1 hodina)

### Chybové kódy

#### Štandardné HTTP status kódy

| Kód | Názov | Popis |
|-----|-------|-------|
| 200 | OK | Úspešná požiadavka |
| 201 | Created | Zdroj bol úspešne vytvorený |
| 400 | Bad Request | Chybné parametre požiadavky |
| 401 | Unauthorized | Chýba alebo je neplatná autentifikácia |
| 403 | Forbidden | Prístup zamietnutý |
| 404 | Not Found | Zdroj nebol nájdený |
| 409 | Conflict | Konflikt pri spracovaní požiadavky |
| 413 | Payload Too Large | Požiadavka je príliš veľká |
| 429 | Too Many Requests | Prekročený limit požiadaviek |
| 500 | Internal Server Error | Vnútorná chyba servera |

#### Formát chybových odpovedí

```json
{
  "error": {
    "code": "INVALID_DOCUMENT",
    "message": "Dokument obsahuje neplatný obsah",
    "details": "Base64 kódovanie je poškodené",
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

### Rate Limiting

API implementuje rate limiting pre zabránenie zneužitia:

- **Signatus2 API**: 100 požiadaviek za minútu na token
- **STS API**: 50 požiadaviek za minútu na API kľúč

Pri prekročení limitu server vráti HTTP status `429 Too Many Requests`.

### Bezpečnosť

#### HTTPS
Všetka komunikácia musí prebiehať cez HTTPS protokol.

#### CSRF ochrana
Pre zvýšenie bezpečnosti je možné použiť CSRF token v hlavičke:
```
CSRF-SIGNATUS-TOKEN: 0bed913d-90bc-48be-ba95-55c8e63da864
```

#### API kľúče
API kľúče musia byť chránené a nesmú byť zdieľané. V prípade kompromitácie kontaktujte podporu.

## Príklady implementácie

### Java

```java
// Signatus2 API - Získanie tokenu
public class SignatusClient {
    private static final String AUTH_URL = "https://dsoauth.ana.sk/realms/Signatus/protocol/openid-connect/token";
    private static final String API_URL = "https://ds-sts.ana.sk";
    
    public String getAccessToken() throws IOException {
        HttpClient client = HttpClient.newHttpClient();
        
        String credentials = Base64.getEncoder()
            .encodeToString("signatus:fb5VXRnMZ2IbAIlik7uBS1mIZE2PJdBN".getBytes());
        
        String formData = "grant_type=password&username=iauser&password=S%23DgfsH64&scope=openid";
        
        HttpRequest request = HttpRequest.newBuilder()
            .uri(URI.create(AUTH_URL))
            .header("Content-Type", "application/x-www-form-urlencoded")
            .header("Authorization", "Basic " + credentials)
            .POST(HttpRequest.BodyPublishers.ofString(formData))
            .build();
        
        HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
        
        if (response.statusCode() == 200) {
            JsonObject json = JsonParser.parseString(response.body()).getAsJsonObject();
            return json.get("access_token").getAsString();
        }
        
        throw new RuntimeException("Failed to get access token: " + response.statusCode());
    }
    
    public JsonObject getEnvelope(String envelopeId, String accessToken) throws IOException {
        HttpClient client = HttpClient.newHttpClient();
        
        HttpRequest request = HttpRequest.newBuilder()
            .uri(URI.create(API_URL + "/workflow/api/envelope/" + envelopeId))
            .header("Authorization", "Bearer " + accessToken)
            .header("Content-Type", "application/x-www-form-urlencoded")
            .GET()
            .build();
        
        HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
        
        if (response.statusCode() == 200) {
            return JsonParser.parseString(response.body()).getAsJsonObject();
        }
        
        throw new RuntimeException("Failed to get envelope: " + response.statusCode());
    }
}
```

### .NET

```csharp
// STS API - Vytvorenie dokumentu
using System;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;

public class STSClient
{
    private static readonly string API_KEY = "961657375627_7d19b177854d7c95c2a0147d42585b613bfe71cde3263bf19d37cb16131b9e43695d74907a3c346b1f708e6c1fa2df1d4fb2eb81b1df145964187c64862f81df";
    private static readonly string BASE_URL = "https://ds-sts.ana.sk";
    private readonly HttpClient _httpClient;
    
    public STSClient()
    {
        _httpClient = new HttpClient();
        _httpClient.DefaultRequestHeaders.Add("x-api-key", API_KEY);
    }
    
    public async Task<DocumentResponse> CreateDocumentAsync(CreateDocumentRequest request)
    {
        var json = JsonConvert.SerializeObject(request);
        var content = new StringContent(json, Encoding.UTF8, "application/json");
        
        var response = await _httpClient.PostAsync($"{BASE_URL}/api/document", content);
        
        if (response.IsSuccessStatusCode)
        {
            var responseJson = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<DocumentResponse>(responseJson);
        }
        
        throw new HttpRequestException($"Failed to create document: {response.StatusCode}");
    }
    
    public async Task<DocumentInfo> GetDocumentAsync(string documentId)
    {
        var response = await _httpClient.GetAsync($"{BASE_URL}/api/document/{documentId}");
        
        if (response.IsSuccessStatusCode)
        {
            var responseJson = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<DocumentInfo>(responseJson);
        }
        
        throw new HttpRequestException($"Failed to get document: {response.StatusCode}");
    }
}

public class CreateDocumentRequest
{
    [JsonProperty("resulturl")]
    public string ResultUrl { get; set; }
    
    [JsonProperty("state")]
    public string State { get; set; }
    
    [JsonProperty("expirationTime")]
    public string ExpirationTime { get; set; }
    
    [JsonProperty("rules")]
    public string Rules { get; set; }
    
    [JsonProperty("filedata")]
    public FileData FileData { get; set; }
}

public class FileData
{
    [JsonProperty("content")]
    public string Content { get; set; }
}

public class DocumentResponse
{
    [JsonProperty("documentId")]
    public string DocumentId { get; set; }
    
    [JsonProperty("result")]
    public string Result { get; set; }
    
    [JsonProperty("status")]
    public string Status { get; set; }
}
```

### Python

```python
# Python implementácia pre oba API
import requests
import base64
import json
from typing import Dict, Any, Optional

class SignatusClient:
    def __init__(self):
        self.auth_url = "https://dsoauth.ana.sk/realms/Signatus/protocol/openid-connect/token"
        self.api_url = "https://ds-sts.ana.sk"
        self.access_token = None
    
    def get_access_token(self) -> str:
        """Získanie OAuth tokenu pre Signatus2 API"""
        credentials = base64.b64encode(b"signatus:fb5VXRnMZ2IbAIlik7uBS1mIZE2PJdBN").decode()
        
        headers = {
            "Content-Type": "application/x-www-form-urlencoded",
            "Authorization": f"Basic {credentials}"
        }
        
        data = {
            "grant_type": "password",
            "username": "iauser",
            "password": "S#DgfsH64",
            "scope": "openid"
        }
        
        response = requests.post(self.auth_url, headers=headers, data=data)
        
        if response.status_code == 200:
            token_data = response.json()
            self.access_token = token_data["access_token"]
            return self.access_token
        
        raise Exception(f"Failed to get access token: {response.status_code}")
    
    def get_envelope(self, envelope_id: str) -> Dict[str, Any]:
        """Získanie informácií o obálke"""
        if not self.access_token:
            self.get_access_token()
        
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/x-www-form-urlencoded"
        }
        
        response = requests.get(
            f"{self.api_url}/workflow/api/envelope/{envelope_id}",
            headers=headers
        )
        
        if response.status_code == 200:
            return response.json()
        elif response.status_code == 404:
            raise Exception(f"Envelope {envelope_id} not found")
        elif response.status_code == 401:
            raise Exception("Unauthorized - invalid or expired token")
        
        raise Exception(f"Failed to get envelope: {response.status_code}")

class STSClient:
    def __init__(self, api_key: str = None):
        self.api_key = api_key or "961657375627_7d19b177854d7c95c2a0147d42585b613bfe71cde3263bf19d37cb16131b9e43695d74907a3c346b1f708e6c1fa2df1d4fb2eb81b1df145964187c64862f81df"
        self.base_url = "https://ds-sts.ana.sk"
    
    def create_document(self, file_content: str, result_url: str, 
                       expiration_time: str, rules: str) -> Dict[str, Any]:
        """Vytvorenie nového dokumentu"""
        headers = {
            "x-api-key": self.api_key,
            "Content-Type": "application/json"
        }
        
        data = {
            "resulturl": result_url,
            "state": "tosign",
            "expirationTime": expiration_time,
            "rules": rules,
            "filedata": {
                "content": file_content
            }
        }
        
        response = requests.post(
            f"{self.base_url}/api/document",
            headers=headers,
            json=data
        )
        
        if response.status_code == 201:
            return response.json()
        elif response.status_code == 401:
            raise Exception("Unauthorized - invalid API key")
        elif response.status_code == 413:
            raise Exception("File too large")
        
        raise Exception(f"Failed to create document: {response.status_code}")
    
    def get_document(self, document_id: str) -> Dict[str, Any]:
        """Získanie informácií o dokumente"""
        headers = {
            "x-api-key": self.api_key
        }
        
        response = requests.get(
            f"{self.base_url}/api/document/{document_id}",
            headers=headers
        )
        
        if response.status_code == 200:
            return response.json()
        elif response.status_code == 404:
            raise Exception(f"Document {document_id} not found")
        elif response.status_code == 401:
            raise Exception("Unauthorized - invalid API key")
        
        raise Exception(f"Failed to get document: {response.status_code}")
    
    def create_signature(self, document_id: str, signer_email: str, 
                        signer_name: str) -> Dict[str, Any]:
        """Vytvorenie nového podpisu"""
        headers = {
            "x-api-key": self.api_key,
            "Content-Type": "application/json"
        }
        
        data = {
            "documentId": document_id,
            "signerEmail": signer_email,
            "signerName": signer_name
        }
        
        response = requests.post(
            f"{self.base_url}/api/signature",
            headers=headers,
            json=data
        )
        
        if response.status_code == 201:
            return response.json()
        elif response.status_code == 404:
            raise Exception(f"Document {document_id} not found")
        elif response.status_code == 401:
            raise Exception("Unauthorized - invalid API key")
        
        raise Exception(f"Failed to create signature: {response.status_code}")
    
    def sign_document(self, signature_id: str) -> Dict[str, Any]:
        """Podpísanie dokumentu"""
        headers = {
            "x-api-key": self.api_key,
            "Content-Type": "application/json"
        }
        
        response = requests.post(
            f"{self.base_url}/api/signature/{signature_id}/sign",
            headers=headers
        )
        
        if response.status_code == 200:
            return response.json()
        elif response.status_code == 404:
            raise Exception(f"Signature {signature_id} not found")
        elif response.status_code == 409:
            raise Exception("Document already signed")
        elif response.status_code == 401:
            raise Exception("Unauthorized - invalid API key")
        
        raise Exception(f"Failed to sign document: {response.status_code}")

# Príklad použitia
if __name__ == "__main__":
    # Signatus2 API
    signatus_client = SignatusClient()
    
    try:
        # Získanie tokenu
        token = signatus_client.get_access_token()
        print(f"Access token: {token[:20]}...")
        
        # Získanie informácií o obálke
        envelope_info = signatus_client.get_envelope("envelope_123")
        print(f"Envelope state: {envelope_info.get('state')}")
        
    except Exception as e:
        print(f"Signatus2 API error: {e}")
    
    # STS API
    sts_client = STSClient()
    
    try:
        # Vytvorenie dokumentu
        with open("document.pdf", "rb") as f:
            file_content = base64.b64encode(f.read()).decode()
        
        document = sts_client.create_document(
            file_content=file_content,
            result_url="https://www.example.com/result",
            expiration_time="1902288000000",
            rules="emailNotification_emailFinal-test@example.com_emailTo-test@example.com_emailFrom-sender@example.com_nameFrom-Sender_language-sk"
        )
        
        document_id = document["documentId"]
        print(f"Created document: {document_id}")
        
        # Vytvorenie podpisu
        signature = sts_client.create_signature(
            document_id=document_id,
            signer_email="<EMAIL>",
            signer_name="John Doe"
        )
        
        signature_id = signature["signatureId"]
        print(f"Created signature: {signature_id}")
        
        # Podpísanie dokumentu
        signed = sts_client.sign_document(signature_id)
        print(f"Document signed at: {signed['signedAt']}")
        
    except Exception as e:
        print(f"STS API error: {e}")
```

---

**Poznámka:** Táto dokumentácia je určená pre externých dodávateľov a vývojárov, ktorí sa pripájajú na Signatus REST služby. Pre najnovšie informácie a aktualizácie API kontaktujte technickú podporu.


## Migračný sprievodca

### Migrácia z verzie 1.0 na 2.0

#### Zmeny v URL adresách

**Staré API (v1.0):**
```
https://ds-sts.ana.sk/workflow/api/envelope
```

**Nové API (v2.0):**
```
https://ds-sts.ana.sk/signatus/api/envelope
```

#### Zmeny v vytváraní obálky

**Staré API (v1.0):**
```python
# Staré API - JSON request
envelope = client.create_envelope(
    name="Test obálka",
    description="Popis",
    recipients=[{"email": "<EMAIL>", "name": "Test"}]
)

# Následné pridávanie dokumentov
client.add_document_to_envelope(envelope_id, "doc.pdf", content)
```

**Nové API (v2.0):**
```python
# Nové API - form-data s dokumentmi
documents_data = {"document.pdf": file_content}
envelope = client.create_envelope(
    name="Test obálka",
    email_to="<EMAIL>",
    email_from="<EMAIL>",
    name_from="Sender",
    documents_data=documents_data
)
```

#### Nové funkcie

1. **Kompletné informácie o obálke:**
```python
complete_info = client.get_envelope_complete(envelope_id)
```

2. **Informácie o procese:**
```python
process_info = client.get_process_info(process_id)
```

3. **Dáta obálky:**
```python
envelope_data = client.get_envelope_data(envelope_id)
```

4. **Vymazanie obálky:**
```python
client.delete_envelope(envelope_id)
```

#### Zastarané metódy

- `add_document_to_envelope()` - Dokumenty sa teraz pridávajú pri vytváraní obálky
- `add_recipient_to_envelope()` - Príjemcovia sa definujú v procesoch
- `send_envelope()` - Obálky sa automaticky aktivujú pri vytvorení

#### Kompatibilita

Demo aplikácia zachováva backward compatibility pre základné operácie, ale odporúčame migráciu na nové API pre plnú funkcionalnost.

---

**Verzia dokumentácie:** 2.0
**Posledná aktualizácia:** Január 2024
**Zmeny:** Aktualizácia podľa najnovšej Postman kolekcie
