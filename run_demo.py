#!/usr/bin/env python3
"""
Spúšťací script pre Signatus2 API Demo

Tento script poskytuje jednoduché menu pre testovanie rôznych funkcií API.
"""

import sys
import os
import base64
from typing import Optional

# Pridanie aktuálneho adresára do Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from signatus2_demo import (
    Signatus2Client, setup_logging, load_sample_pdf,
    Signatus2Error, AuthenticationError, NotFoundError, ConflictError
)


def print_menu():
    """Zobrazenie hlavného menu"""
    print("\n" + "="*60)
    print("    SIGNATUS2 API DEMO - INTERAKTÍVNE MENU (AKTUALIZOVANÉ)")
    print("="*60)
    print("1. Test autentifikácie")
    print("2. Vytvorenie obálky (nové API)")
    print("3. Získanie informácií o obálke")
    print("4. Získanie kompletných informácií o obálke")
    print("5. Získanie dát obálky")
    print("6. Získanie informácií o procese")
    print("7. Vymazanie obálky")
    print("8. Kompletný workflow demo")
    print("9. Nastavenia a konfigurácia")
    print("0. Ukončiť")
    print("-"*60)


def test_authentication(client: Signatus2Client) -> bool:
    """Test autentifikácie"""
    print("\n🔐 Testovanie autentifikácie...")
    try:
        token = client.authenticate()
        print(f"✅ Autentifikácia úspešná!")
        print(f"   Token: {token[:30]}...")
        print(f"   Platnosť do: {client.token_expires_at}")
        return True
    except AuthenticationError as e:
        print(f"❌ Chyba autentifikácie: {e}")
        return False
    except Exception as e:
        print(f"❌ Neočakávaná chyba: {e}")
        return False


def create_envelope(client: Signatus2Client) -> Optional[str]:
    """Vytvorenie novej obálky podľa nového API"""
    print("\n📁 Vytvorenie novej obálky (nové API)...")

    name = input("Zadajte názov obálky (Enter pre predvolený): ").strip()
    if not name:
        name = f"Demo obálka {os.getpid()}"

    email_to = input("Zadajte email príjemcu (Enter pre predvolený): ").strip()
    if not email_to:
        email_to = "<EMAIL>"

    email_from = input("Zadajte email odosielateľa (Enter pre predvolený): ").strip()
    if not email_from:
        email_from = "<EMAIL>"

    name_from = input("Zadajte meno odosielateľa (Enter pre predvolené): ").strip()
    if not name_from:
        name_from = "Demo Odosielateľ"

    # Možnosť pridania dokumentu
    print("\nChcete pridať dokument?")
    print("1. Áno - použiť vzorový PDF")
    print("2. Áno - načítať súbor z disku")
    print("3. Nie - vytvoriť obálku bez dokumentu")

    doc_choice = input("Vyberte možnosť (1-3): ").strip()
    documents_data = None

    if doc_choice == "1":
        # Vzorový PDF
        pdf_content = load_sample_pdf()
        documents_data = {
            "demo_document.pdf": base64.b64decode(pdf_content)
        }
        print("✓ Vzorový PDF pripravený")
    elif doc_choice == "2":
        # Súbor z disku
        file_path = input("Zadajte cestu k súboru: ").strip()
        if os.path.exists(file_path):
            try:
                with open(file_path, "rb") as f:
                    file_content = f.read()
                filename = os.path.basename(file_path)
                documents_data = {filename: file_content}
                print(f"✓ Súbor {filename} pripravený")
            except Exception as e:
                print(f"❌ Chyba pri čítaní súboru: {e}")
        else:
            print(f"❌ Súbor '{file_path}' neexistuje")

    try:
        envelope = client.create_envelope(
            name=name,
            email_to=email_to,
            email_from=email_from,
            name_from=name_from,
            documents_data=documents_data
        )
        envelope_id = envelope.get("id")
        process_id = envelope.get("processes", [{}])[0].get("id") if envelope.get("processes") else None

        print(f"✅ Obálka vytvorená!")
        print(f"   ID: {envelope_id}")
        print(f"   Stav: {envelope.get('state')}")
        print(f"   Expirácia: {envelope.get('expirationtime')}")
        if process_id:
            print(f"   Proces ID: {process_id}")

        return envelope_id
    except Exception as e:
        print(f"❌ Chyba pri vytváraní obálky: {e}")
        return None


def get_envelope_info(client: Signatus2Client) -> None:
    """Získanie informácií o obálke"""
    print("\n📋 Získanie informácií o obálke...")
    
    envelope_id = input("Zadajte ID obálky: ").strip()
    if not envelope_id:
        print("❌ ID obálky je povinné")
        return
    
    try:
        envelope = client.get_envelope(envelope_id)
        print(f"✅ Informácie o obálke:")
        print(f"   ID: {envelope.get('id')}")
        print(f"   Názov: {envelope.get('name')}")
        print(f"   Stav: {envelope.get('state')}")
        print(f"   Popis: {envelope.get('description', 'N/A')}")
        print(f"   Vytvorená: {envelope.get('created', 'N/A')}")
        print(f"   Upravená: {envelope.get('modified', 'N/A')}")
        
        # Dokumenty
        documents = envelope.get('documents', [])
        print(f"   Dokumenty ({len(documents)}):")
        for doc in documents:
            print(f"     - {doc.get('name')} (ID: {doc.get('id')}, Stav: {doc.get('status')})")
        
        # Príjemcovia
        recipients = envelope.get('recipients', [])
        print(f"   Príjemcovia ({len(recipients)}):")
        for recipient in recipients:
            print(f"     - {recipient.get('name')} ({recipient.get('email')}) - {recipient.get('role')}")
            
    except NotFoundError:
        print(f"❌ Obálka s ID '{envelope_id}' nebola nájdená")
    except Exception as e:
        print(f"❌ Chyba pri získavaní obálky: {e}")


def get_envelope_complete_info(client: Signatus2Client) -> None:
    """Získanie kompletných informácií o obálke"""
    print("\n📋 Získanie kompletných informácií o obálke...")

    envelope_id = input("Zadajte ID obálky: ").strip()
    if not envelope_id:
        print("❌ ID obálky je povinné")
        return

    try:
        envelope = client.get_envelope_complete(envelope_id)
        print(f"✅ Kompletné informácie o obálke:")
        print(f"   ID: {envelope.get('id')}")
        print(f"   Stav: {envelope.get('state')}")
        print(f"   Typ: {envelope.get('envelopetype', 'N/A')}")
        print(f"   Expirácia: {envelope.get('expirationtime', 'N/A')}")

        # Procesy
        processes = envelope.get('processes', [])
        print(f"   Procesy ({len(processes)}):")
        for process in processes:
            print(f"     - ID: {process.get('id')}, Stav: {process.get('state')}")
            print(f"       Podpisovateľ: {process.get('signer')}")
            print(f"       Typ podpisu: {process.get('sigtype')}")

    except NotFoundError:
        print(f"❌ Obálka s ID '{envelope_id}' nebola nájdená")
    except Exception as e:
        print(f"❌ Chyba pri získavaní kompletných informácií: {e}")


def get_envelope_data_info(client: Signatus2Client) -> None:
    """Získanie dát obálky"""
    print("\n📊 Získanie dát obálky...")

    envelope_id = input("Zadajte ID obálky: ").strip()
    if not envelope_id:
        print("❌ ID obálky je povinné")
        return

    try:
        data = client.get_envelope_data(envelope_id)
        print(f"✅ Dáta obálky získané:")
        print(f"   Dĺžka dát: {len(data)} znakov")
        print(f"   Prvých 200 znakov: {data[:200]}...")

        # Možnosť uloženia do súboru
        save = input("\nChcete uložiť dáta do súboru? (ano/nie): ").strip().lower()
        if save in ['ano', 'yes', 'y', 'a']:
            filename = f"envelope_data_{envelope_id}.txt"
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(data)
            print(f"✅ Dáta uložené do súboru: {filename}")

    except NotFoundError:
        print(f"❌ Obálka s ID '{envelope_id}' nebola nájdená")
    except Exception as e:
        print(f"❌ Chyba pri získavaní dát obálky: {e}")


def get_process_info_demo(client: Signatus2Client) -> None:
    """Získanie informácií o procese"""
    print("\n⚙️ Získanie informácií o procese...")

    process_id = input("Zadajte ID procesu: ").strip()
    if not process_id:
        print("❌ ID procesu je povinné")
        return

    try:
        process = client.get_process_info(process_id)
        print(f"✅ Informácie o procese:")
        print(f"   ID: {process.get('id')}")
        print(f"   Obálka ID: {process.get('envelopeid')}")
        print(f"   Stav: {process.get('state')}")
        print(f"   Podpisovateľ: {process.get('signer')}")
        print(f"   Typ podpisu: {process.get('sigtype')}")
        print(f"   Podpisovanie: {process.get('signing')}")

        # Dokumenty procesu
        docs_process = process.get('documentsProcess', [])
        print(f"   Dokumenty procesu ({len(docs_process)}):")
        for doc in docs_process:
            print(f"     - ID: {doc.get('documentid')}")
            print(f"       Referencia: {doc.get('reference')}")
            print(f"       Metóda: {doc.get('method')}")

    except NotFoundError:
        print(f"❌ Proces s ID '{process_id}' nebol nájdený")
    except Exception as e:
        print(f"❌ Chyba pri získavaní informácií o procese: {e}")


def delete_envelope_demo(client: Signatus2Client) -> None:
    """Vymazanie obálky"""
    print("\n🗑️ Vymazanie obálky...")

    envelope_id = input("Zadajte ID obálky: ").strip()
    if not envelope_id:
        print("❌ ID obálky je povinné")
        return

    print("⚠️  POZOR: Táto operácia nevratne vymaže obálku!")
    confirm = input("Pokračovať? (ano/nie): ").strip().lower()

    if confirm not in ['ano', 'yes', 'y', 'a']:
        print("❌ Operácia zrušená")
        return

    try:
        result = client.delete_envelope(envelope_id)
        print(f"✅ Obálka vymazaná!")
        print(f"   Správa: {result.get('message', 'OK')}")
    except NotFoundError:
        print(f"❌ Obálka s ID '{envelope_id}' nebola nájdená")
    except Exception as e:
        print(f"❌ Chyba pri mazaní obálky: {e}")


def add_recipient(client: Signatus2Client) -> None:
    """Pridanie príjemcu do obálky"""
    print("\n👤 Pridanie príjemcu do obálky...")
    
    envelope_id = input("Zadajte ID obálky: ").strip()
    if not envelope_id:
        print("❌ ID obálky je povinné")
        return
    
    email = input("Zadajte email príjemcu: ").strip()
    if not email:
        print("❌ Email je povinný")
        return
    
    name = input("Zadajte meno príjemcu: ").strip()
    if not name:
        name = email.split('@')[0]
    
    role = input("Zadajte rolu (signer/viewer, Enter pre 'signer'): ").strip()
    if not role:
        role = "signer"
    
    try:
        result = client.add_recipient_to_envelope(envelope_id, email, name, role)
        print(f"✅ Príjemca pridaný!")
        print(f"   Email: {email}")
        print(f"   Meno: {name}")
        print(f"   Rola: {role}")
    except Exception as e:
        print(f"❌ Chyba pri pridávaní príjemcu: {e}")


def add_document(client: Signatus2Client) -> None:
    """Pridanie dokumentu do obálky"""
    print("\n📄 Pridanie dokumentu do obálky...")
    
    envelope_id = input("Zadajte ID obálky: ").strip()
    if not envelope_id:
        print("❌ ID obálky je povinné")
        return
    
    print("Možnosti dokumentu:")
    print("1. Použiť vzorový PDF")
    print("2. Načítať súbor z disku")
    
    choice = input("Vyberte možnosť (1-2): ").strip()
    
    if choice == "1":
        # Vzorový PDF
        document_name = "Demo dokument.pdf"
        document_content = load_sample_pdf()
        content_type = "application/pdf"
    elif choice == "2":
        # Súbor z disku
        file_path = input("Zadajte cestu k súboru: ").strip()
        if not os.path.exists(file_path):
            print(f"❌ Súbor '{file_path}' neexistuje")
            return
        
        try:
            import base64
            with open(file_path, "rb") as f:
                file_content = f.read()
            document_content = base64.b64encode(file_content).decode()
            document_name = os.path.basename(file_path)
            
            # Určenie MIME typu
            if file_path.lower().endswith('.pdf'):
                content_type = "application/pdf"
            elif file_path.lower().endswith(('.doc', '.docx')):
                content_type = "application/msword"
            elif file_path.lower().endswith('.txt'):
                content_type = "text/plain"
            else:
                content_type = "application/octet-stream"
                
        except Exception as e:
            print(f"❌ Chyba pri čítaní súboru: {e}")
            return
    else:
        print("❌ Neplatná voľba")
        return
    
    try:
        result = client.add_document_to_envelope(
            envelope_id, document_name, document_content, content_type
        )
        print(f"✅ Dokument pridaný!")
        print(f"   Názov: {document_name}")
        print(f"   ID: {result.get('id')}")
        print(f"   Typ: {content_type}")
    except Exception as e:
        print(f"❌ Chyba pri pridávaní dokumentu: {e}")


def send_envelope(client: Signatus2Client) -> None:
    """Odoslanie obálky"""
    print("\n📤 Odoslanie obálky...")
    
    envelope_id = input("Zadajte ID obálky: ").strip()
    if not envelope_id:
        print("❌ ID obálky je povinné")
        return
    
    print("⚠️  POZOR: Táto operácia skutočne odošle obálku príjemcom!")
    confirm = input("Pokračovať? (ano/nie): ").strip().lower()
    
    if confirm not in ['ano', 'yes', 'y', 'a']:
        print("❌ Operácia zrušená")
        return
    
    try:
        result = client.send_envelope(envelope_id)
        print(f"✅ Obálka odoslaná!")
        print(f"   Stav: {result.get('status', 'N/A')}")
    except Exception as e:
        print(f"❌ Chyba pri odosielaní obálky: {e}")


def complete_workflow_demo(client: Signatus2Client) -> None:
    """Kompletný workflow demo s novým API"""
    print("\n🚀 Kompletný workflow demo (aktualizované API)...")
    print("Tento demo vytvorí obálku s dokumentom pomocou nového API.")

    confirm = input("Pokračovať? (ano/nie): ").strip().lower()
    if confirm not in ['ano', 'yes', 'y', 'a']:
        print("❌ Demo zrušené")
        return

    try:
        # 1. Autentifikácia
        print("\n1️⃣ Autentifikácia...")
        if not test_authentication(client):
            return

        # 2. Vytvorenie obálky s dokumentom
        print("\n2️⃣ Vytvorenie obálky s dokumentom...")

        # Príprava dokumentu
        pdf_content = load_sample_pdf()
        documents_data = {
            "workflow_demo.pdf": base64.b64decode(pdf_content)
        }

        try:
            envelope = client.create_envelope(
                name="Workflow Demo - Aktualizované API",
                email_to="<EMAIL>",
                email_from="<EMAIL>",
                name_from="Workflow Demo",
                documents_data=documents_data
            )
            envelope_id = envelope.get("id")
            process_id = envelope.get("processes", [{}])[0].get("id") if envelope.get("processes") else None

            print(f"✅ Obálka vytvorená s ID: {envelope_id}")
            if process_id:
                print(f"✅ Proces ID: {process_id}")

        except Exception as e:
            print(f"❌ Chyba pri vytváraní obálky: {e}")
            return

        # 3. Testovanie nových endpointov
        print("\n3️⃣ Testovanie nových endpointov...")

        # Základné informácie
        try:
            envelope_info = client.get_envelope(envelope_id)
            print(f"✅ Základné info - stav: {envelope_info.get('state')}")
        except Exception as e:
            print(f"❌ Chyba pri získavaní základných info: {e}")

        # Kompletné informácie
        try:
            complete_info = client.get_envelope_complete(envelope_id)
            print(f"✅ Kompletné info - typ: {complete_info.get('envelopetype')}")
        except Exception as e:
            print(f"❌ Chyba pri získavaní kompletných info: {e}")

        # Dáta obálky
        try:
            envelope_data = client.get_envelope_data(envelope_id)
            print(f"✅ Dáta obálky (dĺžka: {len(envelope_data)} znakov)")
        except Exception as e:
            print(f"❌ Chyba pri získavaní dát: {e}")

        # Informácie o procese
        if process_id:
            try:
                process_info = client.get_process_info(process_id)
                print(f"✅ Info o procese - stav: {process_info.get('state')}")
            except Exception as e:
                print(f"❌ Chyba pri získavaní info o procese: {e}")

        # 4. Finálny súhrn
        print("\n4️⃣ Finálny súhrn...")
        print(f"   📁 Obálka ID: {envelope_id}")
        print(f"   ⚙️ Proces ID: {process_id or 'N/A'}")
        print(f"   📄 Dokumenty: Pridané")
        print(f"   🔗 API verzia: Aktualizované")

        # Možnosť vymazania
        delete_choice = input("\n🗑️ Chcete vymazať testovaciu obálku? (ano/nie): ").strip().lower()
        if delete_choice in ['ano', 'yes', 'y', 'a']:
            try:
                delete_result = client.delete_envelope(envelope_id)
                print(f"✅ Obálka vymazaná: {delete_result.get('message', 'OK')}")
            except Exception as e:
                print(f"❌ Chyba pri mazaní: {e}")

        print("\n🎉 Workflow demo dokončené!")

    except Exception as e:
        print(f"❌ Chyba vo workflow: {e}")


def show_settings() -> None:
    """Zobrazenie nastavení a konfigurácie"""
    print("\n⚙️  Aktuálne nastavenia:")
    print("-"*30)
    
    from signatus2_demo import Config
    config = Config()
    
    print(f"Auth URL: {config.AUTH_URL}")
    print(f"API URL: {config.API_BASE_URL}")
    print(f"Client ID: {config.CLIENT_ID}")
    print(f"Username: {config.USERNAME}")
    print(f"Timeout: {config.REQUEST_TIMEOUT}s")
    print(f"Max Retries: {config.MAX_RETRIES}")
    
    print("\n💡 Pre zmenu nastavení použite environment variables:")
    print("   SIGNATUS_USERNAME, SIGNATUS_PASSWORD, SIGNATUS_API_URL, atď.")
    print("\n📁 Alebo vytvorte .env súbor podľa .env.example")


def main():
    """Hlavná funkcia interaktívneho menu"""
    # Nastavenie loggingu
    setup_logging("INFO")
    
    print("🔧 Inicializácia Signatus2 klienta...")
    
    # Vytvorenie klienta
    client = Signatus2Client()
    
    try:
        while True:
            print_menu()
            choice = input("Vyberte možnosť (0-9): ").strip()

            if choice == "0":
                print("👋 Ukončujem demo. Ďakujeme!")
                break
            elif choice == "1":
                test_authentication(client)
            elif choice == "2":
                create_envelope(client)
            elif choice == "3":
                get_envelope_info(client)
            elif choice == "4":
                get_envelope_complete_info(client)
            elif choice == "5":
                get_envelope_data_info(client)
            elif choice == "6":
                get_process_info_demo(client)
            elif choice == "7":
                delete_envelope_demo(client)
            elif choice == "8":
                complete_workflow_demo(client)
            elif choice == "9":
                show_settings()
            else:
                print("❌ Neplatná voľba. Skúste znovu.")

            input("\nStlačte Enter pre pokračovanie...")
    
    except KeyboardInterrupt:
        print("\n\n👋 Demo prerušené používateľom. Ďakujeme!")
    except Exception as e:
        print(f"\n❌ Neočakávaná chyba: {e}")
    finally:
        client.close()


if __name__ == "__main__":
    main()
