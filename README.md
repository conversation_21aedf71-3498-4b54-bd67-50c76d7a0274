# Signatus2 API Demo (Aktualizované)

Jednoduchá Python demo aplikácia pre komunikáciu so Signatus2 REST API. Aplikácia je aktualizovaná podľa najnovšej verzie Postman kolekcie a demonštruje OAuth autentifikáciu a pokročilé operácie so obálkami a dokumentmi.

## 🆕 Nové funkcie (v.2.0)

- ✅ **Aktualizované API endpointy** - Nové cesty `/signatus/api/`
- ✅ **Nová štruktúra obálok** - Komplexnejšie procesy a dokumenty
- ✅ **Priame nahrávanie súborov** - Podpora form-data s dokumentmi
- ✅ **Nové endpointy** - Procesy, kompletné info, dáta obálky, vymazanie
- ✅ **Vylepšené error handling** - Lepšie spracovanie chýb
- ✅ **Interaktívne demo** - Rozšírené menu s novými funkciami

## Základn<PERSON> funkcie

- ✅ OAuth 2.0 autentifikácia s automatickou obnovou tokenov
- ✅ Vytvorenie a správa obálok (nové API)
- ✅ Pridávanie dokumentov priamo pri vytváraní obálky
- ✅ Správa procesov a podpisovateľov
- ✅ Získavanie detailných informácií o obálkach a procesoch
- ✅ Vymazanie obálok
- ✅ Komplexné error handling
- ✅ Konfigurácia cez environment variables
- ✅ Logging pre debugging
- ✅ Type hints pre lepšiu čitateľnosť kódu

## Požiadavky

- Python 3.7+
- Prístup k internetu
- Platné Signatus2 API credentials

## Inštalácia

1. **Klonujte alebo stiahnite súbory:**
   ```bash
   # Stiahnite súbory: signatus2_demo.py, requirements.txt, README.md
   ```

2. **Vytvorte virtuálne prostredie (odporúčané):**
   ```bash
   python3 -m venv venv

   # Windows
   venv\Scripts\activate

   # Linux/macOS
   source venv/bin/activate
   ```

3. **Nainštalujte závislosti:**
   ```bash
   pip3 install -r requirements.txt
   ```

## Konfigurácia

### Predvolené nastavenia

Aplikácia používa testovacie credentials z dokumentácie:
- **Username:** `iauser`
- **Password:** `S#DgfsH64`
- **Auth URL:** `https://dsoauth.ana.sk/realms/Signatus/protocol/openid-connect/token`
- **API URL:** `https://ds-sts.ana.sk/signatus/api` (aktualizované)

### Environment Variables (voliteľné)

Pre vlastnú konfiguráciu nastavte tieto environment variables:

```bash
# OAuth nastavenia
export SIGNATUS_AUTH_URL="https://dsoauth.ana.sk/realms/Signatus/protocol/openid-connect/token"
export SIGNATUS_API_URL="https://ds-sts.ana.sk"
export SIGNATUS_CLIENT_ID="signatus"
export SIGNATUS_CLIENT_SECRET="fb5VXRnMZ2IbAIlik7uBS1mIZE2PJdBN"

# Používateľské údaje
export SIGNATUS_USERNAME="iauser"
export SIGNATUS_PASSWORD="S#DgfsH64"

# Ostatné nastavenia
export SIGNATUS_TIMEOUT="30"
export SIGNATUS_MAX_RETRIES="3"
```

### Windows PowerShell:
```powershell
$env:SIGNATUS_USERNAME="your_username"
$env:SIGNATUS_PASSWORD="your_password"
```

### Linux/macOS:
```bash
export SIGNATUS_USERNAME="your_username"
export SIGNATUS_PASSWORD="your_password"
```

## Spustenie

### Základné spustenie demo:
```bash
python3 signatus2_demo.py
```

### S debug logovaním:
```bash
# Upravte úroveň loggingu v kóde na "DEBUG"
python3 signatus2_demo.py
```

## Použitie v kóde

### Základný príklad (nové API):

```python
from signatus2_demo import Signatus2Client, setup_logging, load_sample_pdf
import base64

# Nastavenie loggingu
setup_logging("INFO")

# Vytvorenie klienta
with Signatus2Client() as client:
    # Autentifikácia
    token = client.authenticate()
    print(f"Token: {token[:20]}...")

    # Príprava dokumentu
    pdf_content = load_sample_pdf()
    documents_data = {
        "contract.pdf": base64.b64decode(pdf_content)
    }

    # Vytvorenie obálky s dokumentom (nové API)
    envelope = client.create_envelope(
        name="Moja zmluva",
        envelope_type="default",
        state="ready",
        expiration_hours=48,
        result_url="https://myapp.com/result",
        lang="sk",
        email_from="<EMAIL>",
        name_from="Ján Novák",
        email_to="<EMAIL>",
        documents_data=documents_data
    )
    envelope_id = envelope["id"]
    process_id = envelope.get("processes", [{}])[0].get("id")

    print(f"Obálka vytvorená: {envelope_id}")
    print(f"Proces ID: {process_id}")

    # Získanie informácií o obálke
    envelope_info = client.get_envelope(envelope_id)
    print(f"Stav obálky: {envelope_info['state']}")

    # Kompletné informácie
    complete_info = client.get_envelope_complete(envelope_id)
    print(f"Typ obálky: {complete_info['envelopetype']}")

    # Informácie o procese
    if process_id:
        process_info = client.get_process_info(process_id)
        print(f"Stav procesu: {process_info['state']}")

    # Vymazanie obálky (voliteľné)
    # client.delete_envelope(envelope_id)
```

### Pokročilé použitie s vlastnou konfiguráciou:

```python
from signatus2_demo import Signatus2Client, Config

# Vlastná konfigurácia
config = Config()
config.USERNAME = "my_username"
config.PASSWORD = "my_password"
config.REQUEST_TIMEOUT = 60

# Klient s vlastnou konfiguráciou
with Signatus2Client(config) as client:
    # Vaše operácie...
    pass
```

## API Metódy

### Autentifikácia
- `authenticate()` - Získanie OAuth access tokenu

### Obálky (aktualizované)
- `create_envelope(name, envelope_type, state, expiration_hours, ...)` - Vytvorenie novej obálky (nové API)
- `get_envelope(envelope_id)` - Získanie základných informácií o obálke
- `get_envelope_complete(envelope_id)` - Získanie kompletných informácií o obálke 🆕
- `get_envelope_data(envelope_id)` - Získanie raw dát obálky 🆕
- `delete_envelope(envelope_id)` - Vymazanie obálky 🆕

### Procesy 🆕
- `get_process_info(process_id)` - Získanie informácií o procese

### Dokumenty (zastarané metódy)
- `add_document_to_envelope()` - ⚠️ Zastarané - dokumenty sa teraz pridávajú pri vytváraní obálky
- `add_recipient_to_envelope()` - ⚠️ Zastarané - príjemcovia sa definujú v procesoch

## Error Handling

Aplikácia definuje špecifické výjimky:

- `Signatus2Error` - Základná API chyba
- `AuthenticationError` - Chyby autentifikácie (401)
- `NotFoundError` - Zdroj nebol nájdený (404)
- `ConflictError` - Konflikt pri spracovaní (409)

```python
try:
    client.get_envelope("neexistujuce_id")
except NotFoundError as e:
    print(f"Obálka nebola nájdená: {e}")
except AuthenticationError as e:
    print(f"Problém s autentifikáciou: {e}")
except Signatus2Error as e:
    print(f"API chyba: {e}")
    if e.response_data:
        print(f"Detaily: {e.response_data}")
```

## Logging

Aplikácia používa štandardný Python logging modul:

```python
import logging

# Nastavenie úrovne loggingu
logging.basicConfig(level=logging.DEBUG)

# Alebo použite setup_logging funkciu
from signatus2_demo import setup_logging
setup_logging("DEBUG")  # DEBUG, INFO, WARNING, ERROR
```

## Demo Workflow

Hlavná `main()` funkcia demonštruje kompletný workflow:

1. **Autentifikácia** - Získanie OAuth tokenu
2. **Vytvorenie obálky** - Nová obálka s názvom a popisom
3. **Pridanie príjemcu** - Test používateľ ako podpisovateľ
4. **Pridanie dokumentu** - Vzorový PDF dokument
5. **Získanie informácií** - Kontrola stavu obálky
6. **Odoslanie** - (zakomentované, odkomentujte pre skutočné odoslanie)

## Bezpečnosť

⚠️ **Dôležité bezpečnostné upozornenia:**

1. **Nikdy necommitujte credentials do verzionovania**
2. **Používajte environment variables pre produkčné nasadenie**
3. **Chráňte API tokeny a kľúče**
4. **Používajte HTTPS pre všetku komunikáciu**

## Troubleshooting

### Časté problémy:

1. **401 Unauthorized**
   - Skontrolujte username/password
   - Overte client_id/client_secret
   - Skontrolujte či nie je token expirovaný

2. **404 Not Found**
   - Overte správnosť envelope_id
   - Skontrolujte API URL

3. **Timeout chyby**
   - Zvýšte SIGNATUS_TIMEOUT
   - Skontrolujte internetové pripojenie

4. **SSL chyby**
   - Aktualizujte certifikáty
   - Skontrolujte firewall nastavenia

### Debug režim:

```python
# Zapnutie debug loggingu
setup_logging("DEBUG")

# Alebo priamo v kóde
import logging
logging.getLogger("requests").setLevel(logging.DEBUG)
logging.getLogger("urllib3").setLevel(logging.DEBUG)
```

## Podpora

Pre technickú podporu a najnovšie informácie:
- 📧 Email: <EMAIL>
- 📖 Dokumentácia: https://docs.signatus.com
- 🔍 Status: https://status.signatus.com

## Licencia

Tento demo kód je poskytnutý na vzdelávacie a testovacie účely.

---

**Verzia:** 1.0  
**Posledná aktualizácia:** Január 2024  
**Python verzia:** 3.7+
