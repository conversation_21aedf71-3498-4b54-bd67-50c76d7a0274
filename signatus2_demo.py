#!/usr/bin/env python3
"""
Signatus2 API Demo Application

Táto aplikácia demonštruje použitie Signatus2 REST API pre správu obálok a dokumentov.
Implementuje OAuth autentifikáciu a základné CRUD operácie.

Autor: Signatus API Demo
Verzia: 1.0
"""

import os
import sys
import json
import base64
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta

import requests
from requests.exceptions import RequestException, HTTPError, Timeout


# Konfiguračné konštanty
class Config:
    """Konfiguračné nastavenia pre Signatus2 API"""

    # URL adresy - AKTUALIZOVANÉ podľa novej Postman kolekcie
    AUTH_URL = os.getenv('SIGNATUS_AUTH_URL', 'https://dsoauth.ana.sk/realms/Signatus/protocol/openid-connect/token')
    API_BASE_URL = os.getenv('SIGNATUS_API_URL', 'https://ds-sts.ana.sk/signatus/api')  # Pridaný /signatus/api prefix

    # OAuth credentials
    CLIENT_ID = os.getenv('SIGNATUS_CLIENT_ID', 'signatus')
    CLIENT_SECRET = os.getenv('SIGNATUS_CLIENT_SECRET', 'fb5VXRnMZ2IbAIlik7uBS1mIZE2PJdBN')

    # Používateľské údaje
    USERNAME = os.getenv('SIGNATUS_USERNAME', 'iauser')
    PASSWORD = os.getenv('SIGNATUS_PASSWORD', 'S#DgfsH64')

    # Ostatné nastavenia
    REQUEST_TIMEOUT = int(os.getenv('SIGNATUS_TIMEOUT', '30'))
    MAX_RETRIES = int(os.getenv('SIGNATUS_MAX_RETRIES', '3'))

    # CSRF token (voliteľný)
    CSRF_TOKEN = os.getenv('SIGNATUS_CSRF_TOKEN', '0bed913d-90bc-48be-ba95-55c8e63da864')


class Signatus2Error(Exception):
    """Základná výjimka pre Signatus2 API chyby"""
    
    def __init__(self, message: str, status_code: Optional[int] = None, response_data: Optional[Dict] = None):
        super().__init__(message)
        self.status_code = status_code
        self.response_data = response_data


class AuthenticationError(Signatus2Error):
    """Výjimka pre chyby autentifikácie"""
    pass


class NotFoundError(Signatus2Error):
    """Výjimka pre chyby typu 404 Not Found"""
    pass


class ConflictError(Signatus2Error):
    """Výjimka pre chyby typu 409 Conflict"""
    pass


class Signatus2Client:
    """
    Klient pre komunikáciu so Signatus2 API
    
    Poskytuje metódy pre OAuth autentifikáciu a správu obálok/dokumentov.
    """
    
    def __init__(self, config: Config = None):
        """
        Inicializácia klienta
        
        Args:
            config: Konfiguračný objekt (voliteľný)
        """
        self.config = config or Config()
        self.access_token: Optional[str] = None
        self.token_expires_at: Optional[datetime] = None
        
        # Nastavenie loggingu
        self.logger = logging.getLogger(__name__)
        
        # HTTP session pre connection pooling
        self.session = requests.Session()
        self.session.timeout = self.config.REQUEST_TIMEOUT
    
    def _get_basic_auth_header(self) -> str:
        """
        Vytvorenie Basic Auth hlavičky pre OAuth
        
        Returns:
            Base64 kódovaný authorization header
        """
        credentials = f"{self.config.CLIENT_ID}:{self.config.CLIENT_SECRET}"
        encoded_credentials = base64.b64encode(credentials.encode()).decode()
        return f"Basic {encoded_credentials}"
    
    def _handle_response(self, response: requests.Response) -> Dict[str, Any]:
        """
        Spracovanie HTTP odpovede a error handling
        
        Args:
            response: HTTP odpoveď
            
        Returns:
            Parsovaná JSON odpoveď
            
        Raises:
            Signatus2Error: Pri chybách API
        """
        try:
            # Pokus o parsovanie JSON odpovede
            if response.content:
                response_data = response.json()
            else:
                response_data = {}
        except json.JSONDecodeError:
            response_data = {"raw_response": response.text}
        
        # Spracovanie rôznych HTTP status kódov
        if response.status_code == 200 or response.status_code == 201:
            return response_data
        elif response.status_code == 401:
            raise AuthenticationError(
                "Neplatná autentifikácia - skontrolujte prihlasovacie údaje",
                status_code=response.status_code,
                response_data=response_data
            )
        elif response.status_code == 404:
            raise NotFoundError(
                "Zdroj nebol nájdený",
                status_code=response.status_code,
                response_data=response_data
            )
        elif response.status_code == 409:
            raise ConflictError(
                "Konflikt pri spracovaní požiadavky",
                status_code=response.status_code,
                response_data=response_data
            )
        else:
            error_message = response_data.get('error', {}).get('message', f'HTTP {response.status_code}')
            raise Signatus2Error(
                f"API chyba: {error_message}",
                status_code=response.status_code,
                response_data=response_data
            )
    
    def authenticate(self) -> str:
        """
        OAuth autentifikácia - získanie access tokenu
        
        Returns:
            Access token
            
        Raises:
            AuthenticationError: Pri chybe autentifikácie
        """
        self.logger.info("Začínam OAuth autentifikáciu...")
        
        headers = {
            "Content-Type": "application/x-www-form-urlencoded",
            "Authorization": self._get_basic_auth_header()
        }
        
        data = {
            "grant_type": "password",
            "username": self.config.USERNAME,
            "password": self.config.PASSWORD,
            "scope": "openid"
        }
        
        try:
            response = self.session.post(
                self.config.AUTH_URL,
                headers=headers,
                data=data,
                timeout=self.config.REQUEST_TIMEOUT
            )
            
            token_data = self._handle_response(response)
            
            self.access_token = token_data["access_token"]
            expires_in = token_data.get("expires_in", 3600)
            self.token_expires_at = datetime.now() + timedelta(seconds=expires_in - 60)  # 60s buffer
            
            self.logger.info("OAuth autentifikácia úspešná")
            return self.access_token
            
        except RequestException as e:
            raise AuthenticationError(f"Chyba pri autentifikácii: {str(e)}")
    
    def _ensure_authenticated(self) -> None:
        """
        Zabezpečenie platnej autentifikácie
        
        Automaticky obnoví token ak je potrebné
        """
        if not self.access_token or (self.token_expires_at and datetime.now() >= self.token_expires_at):
            self.authenticate()
    
    def _get_auth_headers(self) -> Dict[str, str]:
        """
        Získanie autentifikačných hlavičiek
        
        Returns:
            Dictionary s hlavičkami
        """
        self._ensure_authenticated()
        return {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json"
        }
    
    def get_envelope(self, envelope_id: str) -> Dict[str, Any]:
        """
        Získanie informácií o obálke

        Args:
            envelope_id: ID obálky

        Returns:
            Informácie o obálke
        """
        self.logger.info(f"Získavam informácie o obálke {envelope_id}")

        url = f"{self.config.API_BASE_URL}/envelope/{envelope_id}"  # Aktualizovaná cesta
        headers = self._get_auth_headers()
        headers["Content-Type"] = "application/x-www-form-urlencoded"  # Špecifické pre tento endpoint

        try:
            response = self.session.get(url, headers=headers)
            return self._handle_response(response)
        except RequestException as e:
            raise Signatus2Error(f"Chyba pri získavaní obálky: {str(e)}")

    def get_envelope_complete(self, envelope_id: str) -> Dict[str, Any]:
        """
        Získanie kompletných informácií o obálke

        Args:
            envelope_id: ID obálky

        Returns:
            Kompletné informácie o obálke
        """
        self.logger.info(f"Získavam kompletné informácie o obálke {envelope_id}")

        url = f"{self.config.API_BASE_URL}/envelope/complete/{envelope_id}"
        headers = self._get_auth_headers()
        headers["Content-Type"] = "application/x-www-form-urlencoded"

        try:
            response = self.session.get(url, headers=headers)
            return self._handle_response(response)
        except RequestException as e:
            raise Signatus2Error(f"Chyba pri získavaní kompletných informácií o obálke: {str(e)}")

    def get_envelope_data(self, envelope_id: str) -> str:
        """
        Získanie dát obálky (raw data)

        Args:
            envelope_id: ID obálky

        Returns:
            Raw dáta obálky
        """
        self.logger.info(f"Získavam dáta obálky {envelope_id}")

        url = f"{self.config.API_BASE_URL}/envelopeData/{envelope_id}"
        headers = self._get_auth_headers()
        headers["Content-Type"] = "application/x-www-form-urlencoded"

        try:
            response = self.session.get(url, headers=headers)
            if response.status_code == 200:
                return response.text
            else:
                self._handle_response(response)
        except RequestException as e:
            raise Signatus2Error(f"Chyba pri získavaní dát obálky: {str(e)}")

    def delete_envelope(self, envelope_id: str) -> Dict[str, Any]:
        """
        Vymazanie obálky

        Args:
            envelope_id: ID obálky

        Returns:
            Výsledok vymazania
        """
        self.logger.info(f"Mažem obálku {envelope_id}")

        url = f"{self.config.API_BASE_URL}/envelope/{envelope_id}"
        headers = self._get_auth_headers()
        headers["Content-Type"] = "application/x-www-form-urlencoded"

        try:
            response = self.session.delete(url, headers=headers)
            result = self._handle_response(response)
            self.logger.info("Obálka úspešne vymazaná")
            return result
        except RequestException as e:
            raise Signatus2Error(f"Chyba pri mazaní obálky: {str(e)}")
    
    def create_envelope(self, name: str = "Demo obálka", envelope_type: str = "default",
                       state: str = "ready", expiration_hours: int = 24,
                       result_url: str = "https://www.signatus.com",
                       lang: str = "sk", email_from: str = "<EMAIL>",
                       name_from: str = "Demo User", email_to: str = "<EMAIL>",
                       processes: List[Dict[str, Any]] = None,
                       documents_data: Dict[str, bytes] = None) -> Dict[str, Any]:
        """
        Vytvorenie novej obálky podľa novej API štruktúry

        Args:
            name: Názov obálky
            envelope_type: Typ obálky (default)
            state: Stav obálky (ready)
            expiration_hours: Počet hodín do expirácie
            result_url: URL pre presmerovanie
            lang: Jazyk (sk/en)
            email_from: Email odosielateľa
            name_from: Meno odosielateľa
            email_to: Email príjemcu
            processes: Zoznam procesov
            documents_data: Dictionary s názvami súborov a ich obsahom

        Returns:
            Informácie o vytvorenej obálke
        """
        self.logger.info(f"Vytváram novú obálku: {name}")

        url = f"{self.config.API_BASE_URL}/envelope"

        # Výpočet času expirácie
        from datetime import datetime, timedelta
        expiration_time = datetime.now() + timedelta(hours=expiration_hours)
        expiration_timestamp = int(expiration_time.timestamp() * 1000)

        # Predvolené procesy ak nie sú zadané
        if processes is None:
            processes = [
                {
                    "signer": "user1",
                    "signing": "document",
                    "sigtype": "bio",
                    "documents": [
                        {
                            "method": "write",
                            "reference": "demo_document.pdf"
                        }
                    ]
                }
            ]

        # Vytvorenie dataText JSON štruktúry
        data_text = {
            "envelopetype": envelope_type,
            "state": state,
            "type": "menu1",
            "expirationtime": expiration_timestamp,
            "rules": {
                "resulturl": result_url,
                "lang": lang,
                "welcomeScreen": "none",
                "emailFinal": f"{email_from}; {email_to}",
                "emailTo": email_to,
                "emailFrom": email_from,
                "nameFrom": name_from
            },
            "processes": processes,
            "extension": {}
        }

        # Príprava headers
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "CSRF-SIGNATUS-TOKEN": self.config.CSRF_TOKEN
        }

        # Príprava form-data
        files = {
            'dataText': (None, json.dumps(data_text))
        }

        # Pridanie dokumentov ak sú poskytnuté
        if documents_data:
            for filename, file_content in documents_data.items():
                files[filename] = (filename, file_content, 'application/pdf')

        try:
            self._ensure_authenticated()
            response = self.session.post(url, headers=headers, files=files)
            result = self._handle_response(response)
            self.logger.info(f"Obálka vytvorená s ID: {result.get('id')}")
            return result
        except RequestException as e:
            raise Signatus2Error(f"Chyba pri vytváraní obálky: {str(e)}")

    def get_process_info(self, process_id: str) -> Dict[str, Any]:
        """
        Získanie informácií o procese

        Args:
            process_id: ID procesu

        Returns:
            Informácie o procese
        """
        self.logger.info(f"Získavam informácie o procese {process_id}")

        url = f"{self.config.API_BASE_URL}/process/{process_id}"
        headers = self._get_auth_headers()
        headers["Content-Type"] = "application/json"

        try:
            response = self.session.get(url, headers=headers)
            return self._handle_response(response)
        except RequestException as e:
            raise Signatus2Error(f"Chyba pri získavaní informácií o procese: {str(e)}")

    def add_document_to_envelope(self, envelope_id: str, document_name: str,
                               document_content: str, content_type: str = "application/pdf") -> Dict[str, Any]:
        """
        Pridanie dokumentu do obálky
        
        Args:
            envelope_id: ID obálky
            document_name: Názov dokumentu
            document_content: Obsah dokumentu (Base64)
            content_type: MIME typ dokumentu
            
        Returns:
            Informácie o pridanom dokumente
        """
        self.logger.info(f"Pridávam dokument {document_name} do obálky {envelope_id}")
        
        url = f"{self.config.API_BASE_URL}/workflow/api/envelope/{envelope_id}/document"
        headers = self._get_auth_headers()
        
        data = {
            "name": document_name,
            "content": document_content,
            "contentType": content_type
        }
        
        try:
            response = self.session.post(url, headers=headers, json=data)
            result = self._handle_response(response)
            self.logger.info(f"Dokument pridaný s ID: {result.get('id')}")
            return result
        except RequestException as e:
            raise Signatus2Error(f"Chyba pri pridávaní dokumentu: {str(e)}")
    
    def send_envelope(self, envelope_id: str) -> Dict[str, Any]:
        """
        Odoslanie obálky príjemcom
        
        Args:
            envelope_id: ID obálky
            
        Returns:
            Stav odoslania
        """
        self.logger.info(f"Odosielajm obálku {envelope_id}")
        
        url = f"{self.config.API_BASE_URL}/workflow/api/envelope/{envelope_id}/send"
        headers = self._get_auth_headers()
        
        try:
            response = self.session.post(url, headers=headers)
            result = self._handle_response(response)
            self.logger.info("Obálka úspešne odoslaná")
            return result
        except RequestException as e:
            raise Signatus2Error(f"Chyba pri odosielaní obálky: {str(e)}")
    
    def add_recipient_to_envelope(self, envelope_id: str, email: str, name: str, role: str = "signer") -> Dict[str, Any]:
        """
        Pridanie príjemcu do obálky
        
        Args:
            envelope_id: ID obálky
            email: Email príjemcu
            name: Meno príjemcu
            role: Rola príjemcu (signer, viewer, etc.)
            
        Returns:
            Informácie o pridanom príjemcovi
        """
        self.logger.info(f"Pridávam príjemcu {email} do obálky {envelope_id}")
        
        url = f"{self.config.API_BASE_URL}/workflow/api/envelope/{envelope_id}/recipient"
        headers = self._get_auth_headers()
        
        data = {
            "email": email,
            "name": name,
            "role": role
        }
        
        try:
            response = self.session.post(url, headers=headers, json=data)
            result = self._handle_response(response)
            self.logger.info(f"Príjemca pridaný: {email}")
            return result
        except RequestException as e:
            raise Signatus2Error(f"Chyba pri pridávaní príjemcu: {str(e)}")
    
    def close(self) -> None:
        """Zatvorenie HTTP session"""
        if self.session:
            self.session.close()
    
    def __enter__(self):
        """Context manager entry"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.close()


def setup_logging(level: str = "INFO") -> None:
    """
    Nastavenie loggingu
    
    Args:
        level: Úroveň loggingu (DEBUG, INFO, WARNING, ERROR)
    """
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )


def load_sample_pdf() -> str:
    """
    Načítanie vzorového PDF súboru a konverzia do Base64
    
    Returns:
        Base64 kódovaný obsah PDF
    """
    # Jednoduchý PDF obsah pre demo účely
    sample_pdf = b"""%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
72 720 Td
(Hello, Signatus2!) Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000206 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
299
%%EOF"""
    
    return base64.b64encode(sample_pdf).decode()


def main():
    """
    Hlavná demo funkcia
    
    Demonštruje kompletný workflow:
    1. Autentifikácia
    2. Vytvorenie obálky
    3. Pridanie príjemcu
    4. Pridanie dokumentu
    5. Odoslanie obálky
    """
    # Nastavenie loggingu
    setup_logging("INFO")
    logger = logging.getLogger(__name__)
    
    logger.info("=== Signatus2 API Demo ===")
    
    try:
        # Vytvorenie klienta s context managerom
        with Signatus2Client() as client:
            
            # 1. Autentifikácia
            logger.info("1. Autentifikácia...")
            token = client.authenticate()
            logger.info(f"✓ Získaný access token: {token[:20]}...")
            
            # 2. Vytvorenie obálky s dokumentom
            logger.info("\n2. Vytvorenie obálky s dokumentom...")

            # Príprava dokumentu
            pdf_content = load_sample_pdf()
            documents_data = {
                "demo_document.pdf": base64.b64decode(pdf_content)
            }

            envelope = client.create_envelope(
                name="Demo obálka - aktualizované API",
                envelope_type="default",
                state="ready",
                expiration_hours=24,
                result_url="https://www.signatus.com",
                lang="sk",
                email_from="<EMAIL>",
                name_from="Demo Používateľ",
                email_to="<EMAIL>",
                documents_data=documents_data
            )
            envelope_id = envelope.get("id")
            process_id = envelope.get("processes", [{}])[0].get("id") if envelope.get("processes") else None
            logger.info(f"✓ Vytvorená obálka s ID: {envelope_id}")
            if process_id:
                logger.info(f"✓ Proces ID: {process_id}")

            # 3. Získanie informácií o procese (ak existuje)
            if process_id:
                logger.info("\n3. Získanie informácií o procese...")
                try:
                    process_info = client.get_process_info(process_id)
                    logger.info(f"✓ Stav procesu: {process_info.get('state', 'neznámy')}")

                    # Získanie document ID z procesu
                    documents_process = process_info.get('documentsProcess', [])
                    if documents_process:
                        document_id = documents_process[0].get('documentid')
                        logger.info(f"✓ Dokument ID: {document_id}")
                except Exception as e:
                    logger.warning(f"⚠️ Chyba pri získavaní informácií o procese: {e}")

            # 4. Testovanie nových endpointov
            logger.info("\n4. Testovanie nových endpointov...")
            try:
                # Kompletné informácie o obálke
                complete_info = client.get_envelope_complete(envelope_id)
                logger.info(f"✓ Kompletné info - stav: {complete_info.get('state', 'neznámy')}")

                # Dáta obálky
                envelope_data = client.get_envelope_data(envelope_id)
                logger.info(f"✓ Dáta obálky získané (dĺžka: {len(envelope_data)} znakov)")

            except Exception as e:
                logger.warning(f"⚠️ Chyba pri testovaní nových endpointov: {e}")
            
            # 5. Získanie základných informácií o obálke
            logger.info("\n5. Získanie základných informácií o obálke...")
            envelope_info = client.get_envelope(envelope_id)
            logger.info(f"✓ Stav obálky: {envelope_info.get('state', 'neznámy')}")

            # 6. Test vymazania obálky (voliteľné - odkomentujte ak chcete skutočne vymazať)
            # logger.info("\n6. Vymazanie obálky...")
            # delete_result = client.delete_envelope(envelope_id)
            # logger.info(f"✓ Obálka vymazaná: {delete_result.get('message', 'OK')}")

            logger.info("\n=== Demo úspešne dokončené! ===")
            logger.info("💡 Tip: Skúste spustiť 'python run_demo.py' pre interaktívne demo")
            
    except AuthenticationError as e:
        logger.error(f"❌ Chyba autentifikácie: {e}")
        logger.error("Skontrolujte prihlasovacie údaje v konfigurácii")
    except NotFoundError as e:
        logger.error(f"❌ Zdroj nebol nájdený: {e}")
    except ConflictError as e:
        logger.error(f"❌ Konflikt: {e}")
    except Signatus2Error as e:
        logger.error(f"❌ API chyba: {e}")
        if e.response_data:
            logger.error(f"Detaily: {json.dumps(e.response_data, indent=2)}")
    except Exception as e:
        logger.error(f"❌ Neočakávaná chyba: {e}")
        raise


if __name__ == "__main__":
    main()
