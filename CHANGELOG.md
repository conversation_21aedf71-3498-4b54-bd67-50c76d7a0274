# Changelog - Signatus2 API Demo

## Verzia 2.0 (Január 2024) - Aktualizácia podľa najnovšej Postman kolekcie

### 🆕 Nové funkcie

#### API Endpointy
- **Nové API cesty**: Všetky endpointy teraz používajú `/signatus/api/` prefix namiesto `/workflow/api/`
- **Nový endpoint**: `GET /signatus/api/process/{processId}` - Informácie o procese
- **Nový endpoint**: `GET /signatus/api/envelope/complete/{envelopeId}` - Kompletné informácie o obálke
- **Nový endpoint**: `GET /signatus/api/envelopeData/{envelopeId}` - Raw dáta obálky
- **Nový endpoint**: `DELETE /signatus/api/envelope/{envelopeId}` - Vymazanie obálky

#### Vytvorenie obálky
- **Nová štruktúra**: Komplexnejšia štruktúra s `processes`, `documents`, `rules`
- **Form-data podpora**: Priame nahrávanie súborov pri vytváraní obálky
- **Procesy**: Podpora pre rôzne typy podpisov (`bio`, `certificate`, `simple`)
- **Metódy dokumentov**: `write`, `read`, `optional`
- **Email konfigurácia**: Rozšírené nastavenia pre notifikácie

#### Klient (signatus2_demo.py)
- **Nové metódy**:
  - `get_envelope_complete(envelope_id)` - Kompletné informácie
  - `get_envelope_data(envelope_id)` - Raw dáta obálky
  - `get_process_info(process_id)` - Informácie o procese
  - `delete_envelope(envelope_id)` - Vymazanie obálky
- **Aktualizovaná metóda**: `create_envelope()` s novou štruktúrou
- **CSRF token podpora**: Voliteľný CSRF token v hlavičkách

#### Demo aplikácie
- **Rozšírené interaktívne menu**: Nové možnosti v `run_demo.py`
- **Nové príklady**: Aktualizované `examples.py` s novými API funkciami
- **Workflow demo**: Kompletný workflow s novým API

### 🔄 Zmeny

#### Konfigurácia
- **Base URL**: Zmenené z `https://ds-sts.ana.sk` na `https://ds-sts.ana.sk/signatus/api`
- **CSRF token**: Pridaný voliteľný CSRF token do konfigurácie

#### API štruktúra
- **Vytvorenie obálky**: Zmenené z JSON na form-data s dokumentmi
- **Response štruktúra**: Nové polia `processes`, `expirationtime`, `envelopetype`
- **Procesy**: Nová štruktúra s `signer`, `signing`, `sigtype`, `documents`

#### Dokumentácia
- **API dokumentácia**: Kompletne aktualizovaná s novými endpointmi
- **README**: Aktualizované príklady a inštrukcie
- **Migračný sprievodca**: Pridaný sprievodca pre migráciu z v1.0

### 🧪 Testy
- **Nové unit testy**: Testy pre všetky nové metódy
- **Aktualizované mock objekty**: Podľa nových API odpovedí
- **Integračné testy**: Rozšírené pre nové endpointy

### ⚠️ Breaking Changes

#### Zastarané metódy
- `add_document_to_envelope()` - Dokumenty sa teraz pridávajú pri vytváraní obálky
- `add_recipient_to_envelope()` - Príjemcovia sa definujú v procesoch obálky
- `send_envelope()` - Obálky sa automaticky aktivujú pri vytvorení

#### Zmeny v API
- **URL cesty**: Všetky cesty zmenené z `/workflow/api/` na `/signatus/api/`
- **Vytvorenie obálky**: Kompletne nová štruktúra parametrov
- **Response formát**: Nové polia a štruktúra odpovedí

### 🔧 Kompatibilita

#### Backward Compatibility
- **Základné operácie**: Zachovaná kompatibilita pre autentifikáciu a základné získavanie informácií
- **Deprecation warnings**: Pridané upozornenia pre zastarané metódy
- **Migračná podpora**: Možnosť postupnej migrácie

#### Odporúčania
- **Migrácia**: Odporúčame migráciu na nové API pre plnú funkcionalnosť
- **Testovanie**: Otestujte všetky funkcie s novým API
- **Dokumentácia**: Prečítajte si migračný sprievodca

### 📁 Súbory

#### Aktualizované súbory
- `signatus2_demo.py` - Hlavný klient s novými metódami
- `run_demo.py` - Rozšírené interaktívne demo
- `examples.py` - Nové príklady použitia
- `test_signatus2_demo.py` - Aktualizované testy
- `README.md` - Aktualizovaná dokumentácia
- `Signatus_API_Documentation.md` - Kompletne prepracovaná API dokumentácia
- `.env.example` - Aktualizovaná konfigurácia

#### Nové súbory
- `CHANGELOG.md` - Tento súbor so zmenami

### 🚀 Spustenie

#### Rýchly štart s novým API
```bash
# Inštalácia
pip3 install -r requirements.txt

# Spustenie nového demo
python3 signatus2_demo.py

# Interaktívne demo s novými funkciami
python3 run_demo.py

# Príklady nových funkcií
python3 examples.py
```

#### Testovanie
```bash
# Unit testy
python3 -m pytest test_signatus2_demo.py -v

# Pomocou Makefile
make test
```

### 📞 Podpora

Pre otázky ohľadom migrácie alebo nových funkcií:
- 📧 Email: <EMAIL>
- 📖 Dokumentácia: Pozrite si `Signatus_API_Documentation.md`
- 🔍 Migračný sprievodca: Sekcia v dokumentácii

---

**Poznámka**: Táto verzia je plne kompatibilná s najnovšou Postman kolekciou `Signatus2 - CRUD.postman_collection.json`.
