# Signatus2 API Demo - Makefile
# Jednoduch<PERSON><PERSON> spúšťanie a správa projektu

.PHONY: help install test run demo clean lint format check

# Predvolená akcia
help:
	@echo "Signatus2 API Demo - Dostupn<PERSON> príkazy (Python 3):"
	@echo ""
	@echo "  install     - Inštal<PERSON>cia závislostí (pip3)"
	@echo "  test        - <PERSON><PERSON><PERSON><PERSON> testov (python3 -m pytest)"
	@echo "  run         - Spustenie z<PERSON>ladn<PERSON>ho demo (python3)"
	@echo "  demo        - Spustenie interaktívneho demo (python3)"
	@echo "  examples    - Spustenie príkladov (python3)"
	@echo "  clean       - Vyčistenie dočasných súborov"
	@echo "  lint        - <PERSON><PERSON><PERSON><PERSON> kódu (python3 -m flake8)"
	@echo "  format      - Formátov<PERSON>e kódu (python3 -m black)"
	@echo "  check       - Kontrola typov (python3 -m mypy)"
	@echo "  venv        - Vytvorenie virtuálneho prostredia (python3 -m venv)"
	@echo "  info        - Zobrazenie informácií o prostredí"
	@echo ""

# Vytvorenie virtuálneho prostredia
venv:
	python3 -m venv venv
	@echo "Virtuálne prostredie vytvorené. Aktivujte ho:"
	@echo "  Windows: venv\\Scripts\\activate"
	@echo "  Linux/macOS: source venv/bin/activate"

# Inštalácia závislostí
install:
	pip3 install --upgrade pip
	pip3 install -r requirements.txt

# Inštalácia development závislostí
install-dev: install
	pip3 install flake8 black mypy

# Spustenie základného demo
run:
	python3 signatus2_demo.py

# Spustenie interaktívneho demo
demo:
	python3 run_demo.py

# Spustenie testov
test:
	python3 -m pytest test_signatus2_demo.py -v

# Spustenie testov s pokrytím
test-coverage:
	python3 -m pytest test_signatus2_demo.py -v --cov=signatus2_demo --cov-report=html

# Spustenie integračných testov (vyžaduje platné API credentials)
test-integration:
	python3 -m pytest test_signatus2_demo.py -v -m integration

# Kontrola kódu
lint:
	python3 -m flake8 signatus2_demo.py run_demo.py test_signatus2_demo.py

# Formátovanie kódu
format:
	python3 -m black signatus2_demo.py run_demo.py test_signatus2_demo.py

# Kontrola typov
check:
	python3 -m mypy signatus2_demo.py --ignore-missing-imports

# Vyčistenie dočasných súborov
clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	find . -type d -name ".pytest_cache" -exec rm -rf {} +
	find . -type d -name "htmlcov" -exec rm -rf {} +
	find . -type f -name ".coverage" -delete

# Kompletná kontrola kvality kódu
quality: lint check test
	@echo "✅ Kontrola kvality kódu dokončená"

# Príprava na produkciu
build: clean quality
	@echo "✅ Projekt pripravený na nasadenie"

# Zobrazenie informácií o prostredí
info:
	@echo "Python verzia:"
	@python3 --version
	@echo ""
	@echo "Pip verzia:"
	@pip3 --version
	@echo ""
	@echo "Nainštalované balíčky:"
	@pip3 list | grep -E "(requests|pytest|black|flake8|mypy)"

# Rýchly setup pre nových vývojárov
setup: venv
	@echo "Aktivujte virtuálne prostredie a spustite 'make install'"

# Spustenie príkladov
examples:
	python3 examples.py

# Dokumentácia
docs:
	@echo "📖 Dokumentácia:"
	@echo "  README.md - Základné informácie a inštrukcie"
	@echo "  Signatus_API_Documentation.md - Kompletná API dokumentácia"
	@echo "  CHANGELOG.md - Zoznam zmien"
	@echo ""
	@echo "🚀 Rýchly štart:"
	@echo "  1. make venv"
	@echo "  2. source venv/bin/activate  # alebo venv\\Scripts\\activate na Windows"
	@echo "  3. make install"
	@echo "  4. make demo"
	@echo ""
	@echo "🧪 Testovanie:"
	@echo "  make test        # Unit testy"
	@echo "  make examples    # Spustenie príkladov"
	@echo "  make info        # Informácie o prostredí"
