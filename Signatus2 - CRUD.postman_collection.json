{"info": {"_postman_id": "e7df5876-2ae3-4f3c-88cc-01dcfa517219", "name": "Signatus2 - CRUD", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "13311497"}, "item": [{"name": "create envelope 3x Document", "item": [{"name": "1. login oauth", "event": [{"listen": "test", "script": {"exec": ["if ( pm.response.code == '200') {\r", "    pm.test('login - pozitivny scenar', function () {\r", "        pm.response.to.have.status(200);\r", "    })\r", "\r", "    var jsonData = pm.response.json();\r", "    pm.environment.set(\"refresh_token\", jsonData.refresh_token);\r", "    pm.environment.set(\"access_token\", jsonData.access_token);\r", "    pm.environment.set(\"id_token\", jsonData.id_token);\r", "\r", "    pm.test(\"Access token is not empty\", function () {\r", "        pm.expect(jsonData.access_token).to.exist.and.to.not.be.empty;\r", "    });\r", "\r", "    pm.test(\"Refresh token is not empty\", function () {\r", "        pm.expect(jsonData.refresh_token).to.exist.and.to.not.be.empty;\r", "    });\r", "\r", "    pm.test(\"Token type is present and has a valid value\", function () {\r", "        pm.expect(jsonData).to.have.property('token_type');\r", "        pm.expect(jsonData.token_type).to.be.a('string').and.to.not.be.empty;\r", "    });\r", "}else {\r", "    pm.test(('ERROR login user'), () => {\r", "        pm.response.to.have.status(200);\r", "    })\r", "\r", "    pm.execution.setNextRequest(null);   \r", "}\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "basic", "basic": [{"key": "password", "value": "fb5VXRnMZ2IbAIlik7uBS1mIZE2PJdBN", "type": "string"}, {"key": "username", "value": "signatus", "type": "string"}, {"key": "saveHelperData", "value": true, "type": "boolean"}, {"key": "showPassword", "value": false, "type": "boolean"}]}, "method": "POST", "header": [{"key": "CSRF-SIGNATUS-TOKEN", "value": "0bed913d-90bc-48be-ba95-55c8e63da864", "disabled": true}, {"key": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "grant_type", "value": "password", "type": "text"}, {"key": "username", "value": "<PERSON><PERSON><PERSON>", "type": "text"}, {"key": "password", "value": "S#DgfsH64", "type": "text"}, {"key": "client_id", "value": "", "type": "text", "disabled": true}, {"key": "scope", "value": "openid", "type": "text"}]}, "url": {"raw": "https://dsoauth.ana.sk/realms/Signatus/protocol/openid-connect/token", "protocol": "https", "host": ["<PERSON><PERSON><PERSON><PERSON>", "ana", "sk"], "path": ["realms", "Signatus", "protocol", "openid-connect", "token"]}}, "response": []}, {"name": "create envelope -ET_default -bio_3D_write+read", "event": [{"listen": "test", "script": {"exec": ["if ( pm.response.code == '200') {\r", "    pm.test('create envelope - pozitivny scenar', function () {\r", "        pm.response.to.have.status(200);\r", "    })\r", "\r", "    var jsonData = pm.response.json();\r", "    pm.environment.set(\"envelopeId\", jsonData.id);\r", "    pm.environment.set(\"processid\",jsonData.processes[0].id);\r", "\r", "    pm.test(\"Response Content-Type header is application/json\", function () {\r", "        pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/json\");\r", "    });\r", "\r", "    pm.test(\"Id field is present and not empty\", function () {\r", "        pm.expect(jsonData).to.have.property('id').that.is.not.empty;\r", "    });\r", "\r", "    pm.test(\"Expiration time is a non-negative integer\", function () {\r", "    pm.expect(jsonData.expirationtime).to.be.a('number');\r", "    pm.expect(jsonData.expirationtime).to.be.at.least(0);\r", "    });\r", "\r", "} else {\r", "    pm.test(('ERROR login user'), () => {\r", "        pm.response.to.have.status(200);\r", "    })\r", "}"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["let expireEnvelope = new Date();\r", "expireEnvelope.setSeconds(expireEnvelope.getSeconds() + 86400);\r", "pm.environment.set(\"expireEnvelope\", expireEnvelope.getTime());"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "CSRF-SIGNATUS-TOKEN", "value": "0bed913d-90bc-48be-ba95-55c8e63da864", "disabled": true}, {"key": "Content-Type", "value": "application/x-www-form-urlencoded"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "dataText", "value": "{\n\t\"envelopetype\": \"default\",\n\t\"state\": \"ready\",\n\t\"type\": \"menu1\",\n\t\"expirationtime\": {{expireEnvelope}},\n    \"rules\": {\"resulturl\": \"https://www.signatus.com\",\"lang\": \"en\",\"welcomeScreen\":\"none\",\"sendInfo\": \"http://localhost:8080/STSTest/sendinfo\",\n\t\t\"emailFinal\": \"<EMAIL>; Tomas.<PERSON>@anasoft.com\",\n\t\t\"emailTo\": \"<EMAIL>\",\n\t\t\"emailFrom\": \"<EMAIL>\",\n\t\t\"nameFrom\": \"<PERSON>\",\n},\n\t\"processes\": [\n\t\t{\n\t\t\t\"signer\": \"user1\",\n\t\t\t\"signing\": \"document\",\n\t\t\t\"sigtype\": \"bio\",\n\t\t\t\"documents\": [\n\t\t\t\t{\n\t\t\t\t\t\"method\": \"write\",\n\t\t\t\t\t\"reference\": \"lenso_001_003_1.pdf\"\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\t\"method\": \"read\",\n\t\t\t\t\t\"reference\": \"lenso_001_003_2.pdf\"\n\t\t\t\t},\n\t\t\t\t{\n\t\t\t\t\t\"method\": \"optional\",\n\t\t\t\t\t\"reference\": \"lenso_001_003_3.pdf\"\n\t\t\t\t}\n\t\t\t]\n\t\t}\n\t],\n\t\"extension\": {\n\t}\n}", "type": "text"}, {"key": "lenso_001_003_1.pdf", "type": "file", "src": "STS/TestLenso.pdf"}, {"key": "lenso_001_003_2.pdf", "type": "file", "src": "STS/TestLenso.pdf"}, {"key": "lenso_001_003_3.pdf", "type": "file", "src": "STS/TestLenso.pdf"}]}, "url": {"raw": "https://ds-sts.ana.sk/signatus/api/envelope", "protocol": "https", "host": ["ds-sts", "ana", "sk"], "path": ["signatus", "api", "envelope"]}}, "response": []}, {"name": "info process {{processid}}", "event": [{"listen": "test", "script": {"exec": ["if ( pm.response.code == '200') {\r", "    pm.test('info process - pozitivny scenar', function () {\r", "        pm.response.to.have.status(200);\r", "    })\r", "\r", "    var jsonData = pm.response.json();\r", "    pm.environment.set(\"documentid\",jsonData.documentsProcess[0].documentid);\r", "\r", "    pm.test('info process - stav ready', function () {\r", "        pm.expect(jsonData.state).to.eql(\"ready\");\r", "    })\r", "} else {\r", "    let processid = pm.environment.get(\"processid\"); \r", "    pm.test(('ERROR info process id: '+processid), () => {\r", "        pm.response.to.have.status(200);\r", "    })\r", "}"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "CSRF-SIGNATUS-TOKEN", "value": "0bed913d-90bc-48be-ba95-55c8e63da864", "disabled": true}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "https://ds-sts.ana.sk/signatus/api/process/{{processid}}", "protocol": "https", "host": ["ds-sts", "ana", "sk"], "path": ["signatus", "api", "process", "{{processid}}"]}, "description": "\nThis endpoint makes an HTTP GET request to retrieve information about a specific process using the provided process ID. The request should be sent to {{workflowUrl}}/api/process/{{processid}}.\n\nThe request payload for the last call used the form-data request body type, with an empty payload.\n\nThe response to the last execution had a status code of 200 and a content type of application/json. The response body contained information about the process, including the process ID, envelope ID, signer details, state, signing information, signature type, documents related to the process, envelope type ID, and tenant details.\n\nThe user has requested to \"Add documentation\".\n"}, "response": []}, {"name": "info envelope {{envelopeId}}", "event": [{"listen": "test", "script": {"exec": ["if ( pm.response.code == '401' || pm.response.code == '409') {    \r", "    pm.test(('ERROR login user'), () => {\r", "        pm.response.to.have.status(200);\r", "    })\r", "}\r", "else if ( pm.response.code == '404') {\r", "    let envelopeId = pm.environment.get(\"envelopeId\"); \r", "    pm.test(('ERROR get envelope not exist id: '+envelopeId), () => {\r", "        pm.response.to.have.status(200);\r", "    })\r", "}\r", "else {\r", "    var json = pm.response.json();\r", "    pm.test('info envelope - stav final', function () {\r", "        pm.expect(json.state).to.eql(\"final\");\r", "    })\r", "\r", "}"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "CSRF-SIGNATUS-TOKEN", "value": "0bed913d-90bc-48be-ba95-55c8e63da864", "disabled": true}, {"key": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text"}, {"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "https://ds-sts.ana.sk/signatus/api/envelope/{{envelopeId}}", "protocol": "https", "host": ["ds-sts", "ana", "sk"], "path": ["signatus", "api", "envelope", "{{envelopeId}}"]}}, "response": []}, {"name": "info envelope complete {{envelopeId}}", "event": [{"listen": "test", "script": {"exec": ["if ( pm.response.code == '401' || pm.response.code == '409') {    \r", "    pm.test(('ERROR login user'), () => {\r", "        pm.response.to.have.status(200);\r", "    })\r", "}\r", "else if ( pm.response.code == '404') {\r", "    let envelopeId = pm.environment.get(\"envelopeId\"); \r", "    pm.test(('ERROR get envelope not exist id: '+envelopeId), () => {\r", "        pm.response.to.have.status(200);\r", "    })\r", "}\r", "else {\r", "    var json = pm.response.json();\r", "    pm.test('info envelope - stav final', function () {\r", "        pm.expect(json.state).to.eql(\"final\");\r", "    })\r", "\r", "}"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "CSRF-SIGNATUS-TOKEN", "value": "0bed913d-90bc-48be-ba95-55c8e63da864", "disabled": true}, {"key": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text"}, {"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "https://ds-sts.ana.sk/signatus/api/envelope/complete/{{envelopeId}}", "protocol": "https", "host": ["ds-sts", "ana", "sk"], "path": ["signatus", "api", "envelope", "complete", "{{envelopeId}}"]}}, "response": []}, {"name": "envelope data by {{envelopeId}}", "event": [{"listen": "test", "script": {"exec": ["if ( pm.response.code == '401' || pm.response.code == '409') {    \r", "    pm.test(('ERROR login user'), () => {\r", "        pm.response.to.have.status(200);\r", "    })\r", "}\r", "else {\r", "    var text = pm.response.text()\r", "        pm.test(\"Response body is empty \", function () {\r", "        if(text.length < 3) {\r", "            throw new Error(\"Expected length to be greater than 3\");\r", "        }\r", "    });\r", "\r", "}"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "CSRF-SIGNATUS-TOKEN", "value": "0bed913d-90bc-48be-ba95-55c8e63da864", "disabled": true}, {"key": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text"}, {"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "https://ds-sts.ana.sk/signatus/api/envelopeData/{{envelopeId}}", "protocol": "https", "host": ["ds-sts", "ana", "sk"], "path": ["signatus", "api", "envelopeData", "{{envelopeId}}"]}}, "response": []}, {"name": "delete envelope by {{envelopeId}}", "event": [{"listen": "test", "script": {"exec": ["if ( pm.response.code == '401' || pm.response.code == '409') {    \r", "    pm.test(('ERROR login user'), () => {\r", "        pm.response.to.have.status(200);\r", "    })\r", "}\r", "else if ( pm.response.code == '500') {\r", "    let envelopeId = pm.environment.get(\"envelopeId\"); \r", "    pm.test(('ERROR delete envelope  not exist id: '+envelopeId), () => {\r", "        pm.response.to.have.status(200);\r", "    })\r", "}\r", "else {\r", "    pm.test('Delete envelope', function () {\r", "        pm.response.to.have.status(200);\r", "    })\r", "\r", "    var json = pm.response.json();\r", "        pm.test('Delete envelope -message msg.envelope.delete.success', function () {\r", "        pm.expect(json.message).to.eql(\"msg.envelope.delete.success\");\r", "    })\r", "\r", "}\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "DELETE", "header": [{"key": "CSRF-SIGNATUS-TOKEN", "value": "0bed913d-90bc-48be-ba95-55c8e63da864", "disabled": true}, {"key": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text"}, {"key": "Authorization", "value": "Bearer {{access_token}}", "type": "text"}], "body": {"mode": "formdata", "formdata": []}, "url": {"raw": "{{signatusUrl}}/api/envelope/{{envelopeId}}", "host": ["{{signatusUrl}}"], "path": ["api", "envelope", "{{envelopeId}}"]}}, "response": []}]}]}